"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.marriageInfoUpdate = marriageInfoUpdate;
exports.marriageInfoShow = marriageInfoShow;
exports.marriageInfoTransfer = marriageInfoTransfer;
const _ = require("lodash");
const logger_1 = require("../../logger");
const marriageInfo_1 = require("../../models/marriageInfo");
const moment = require("moment");
const models_1 = require("../../models");
const marriageTransfer_1 = require("../../services/marriageTransfer");
const errorCodes_1 = require("../../errorCodes");
const logger = (0, logger_1.clazzLogger)("marriageInfo/operation");
/** 保存的时效是24小时，过期之后数据就可以删了，超出时效的查询，就不用返回数据了 */
const KEEP_EXPIRE_SECONDS = 24 * 3600;
/** 更新情缘信息 (CallByGameServer) */
async function marriageInfoUpdate(params) {
    const { marriageInfo, marriageInfoId, marriageMemory, updateTime } = params;
    const props = {
        id: marriageInfoId,
        info: marriageInfo,
        memory: marriageMemory,
        updateTime: updateTime || Date.now()
    };
    const ret = await marriageInfo_1.MarriageInfoModel.createOrUpdate(props, _.omit(props, ['id']));
    logger.info({ id: marriageInfoId, info: marriageInfo }, "marriageInfoUpdate");
    const isOk = ret.affectedRows > 0;
    const data = { isOk };
    return data;
}
/** 获取情缘信息 */
async function marriageInfoShow(params) {
    const r = await marriageInfo_1.MarriageInfoModel.findOne({ id: params.marriageInfoId });
    if (r && r.id) {
        const isExpired = moment().diff(r.updateTime, 'seconds') > KEEP_EXPIRE_SECONDS;
        if (isExpired) {
            logger.info({ roleId: params.roleId, id: r.id, info: r.info, updateTime: r.updateTime }, "MarriageInfoExpired");
            return null;
        }
        else {
            const data = {
                marriageInfoId: r.id,
                marriageInfo: r.info,
                marriageMemory: r.memory,
                updateTime: r.updateTime
            };
            return data;
        }
    }
    else {
        return null;
    }
}
async function findOriginIdByNewId(id) {
    let record = await models_1.MarriageTransferModel.findOne({ newId: id }, ['id', 'originId']);
    if (record) {
        return record.originId;
    }
    else {
        return 0;
    }
}
/** 通知情缘id转服后变更 (CallByGameServer) */
async function marriageInfoTransfer(params) {
    let record = await models_1.MarriageTransferModel.exists({ newId: params.newId, oldId: params.oldId });
    if (record) {
        throw errorCodes_1.MarriagePhotoWallErrors.MarriageTransferExist;
    }
    let originId = await findOriginIdByNewId(params.oldId);
    let props = {
        oldId: params.oldId,
        newId: params.newId,
        originId: originId || params.oldId,
        createTime: Date.now(),
    };
    let id = await models_1.MarriageTransferModel.insert(props);
    await (0, marriageTransfer_1.addMigrateJob)(params.oldId, params.newId);
    const data = {
        id
    };
    return data;
}
//# sourceMappingURL=operation.js.map
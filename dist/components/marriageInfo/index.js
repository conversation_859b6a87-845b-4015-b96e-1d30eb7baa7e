"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarriageInfoComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/marriage_info/update",
        paramsSchema: type_1.ReqSchemas.MarriageInfoUpdate,
        before: [gameIpLimit_1.gameIpLimit],
        operation: operation_1.marriageInfoUpdate,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/marriage_info/transfer",
        before: [gameIpLimit_1.gameIpLimit],
        paramsSchema: type_1.ReqSchemas.MarriageInfoTransfer,
        operation: operation_1.marriageInfoTransfer,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/marriage_info/show",
        paramsSchema: type_1.ReqSchemas.MarriageInfoShow,
        operation: operation_1.marriageInfoShow,
    },
];
exports.MarriageInfoComponent = {
    paths: exports.paths,
    prefix: "/marriage_info/",
};
//# sourceMappingURL=index.js.map
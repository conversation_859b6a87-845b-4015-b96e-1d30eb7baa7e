"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exportMomentDetailData = exportMomentDetailData;
const _ = require("lodash");
const config_all_1 = require("../../common/config.all");
const util_1 = require("../../common/util");
const models_1 = require("../../models");
const RoleInfos_1 = require("../../models/RoleInfos");
function parseOffsets(offset) {
    if (!offset || offset.split('-').length != 3) {
        return {
            comment: 0,
            forward: 0,
            like: 0,
        };
    }
    const parts = offset.split('-');
    return {
        comment: Number(parts[0]),
        forward: Number(parts[1]),
        like: Number(parts[2]),
    };
}
function genOffsets(commentActionList, forwardActionList, likeActionList) {
    if (commentActionList.list.length < config_all_1.OAMExportLimit.comment && forwardActionList.list.length < config_all_1.OAMExportLimit.forward && likeActionList.list.length < config_all_1.OAMExportLimit.like) {
        return '';
    }
    return `${commentActionList.maxId}-${forwardActionList.maxId}-${likeActionList.maxId}`;
}
async function exportMomentDetailData(momentId, offset) {
    const offsets = parseOffsets(offset);
    const commentActionList = await getCommentActionList(momentId, offsets.comment);
    const forwardActionList = await getForwardActionList(momentId, offsets.forward);
    const likeActionList = await getLikeActionList(momentId, offsets.like);
    const actionList = [].concat(commentActionList.list, forwardActionList.list, likeActionList.list);
    return {
        list: actionList,
        offset: genOffsets(commentActionList, forwardActionList, likeActionList),
    };
}
async function getCommentActionList(momentId, offset) {
    const query = models_1.CommentModel.scope().where("TargetId", momentId).where("Status", 0 /* Statues.Normal */).where("ID", ">=", offset).limit(config_all_1.OAMExportLimit.comment).columns(['ID', 'RoleId', 'Text', 'CreateTime']).orderBy("ID", "ASC");
    const commentList = await models_1.CommentModel.executeByQuery(query);
    const roleIds = _.uniq(commentList.map(item => item.RoleId));
    const roleInfos = await RoleInfos_1.RoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName"] });
    const roleMap = (0, util_1.keyToRecordMap)(roleInfos, "RoleId");
    const commentActionList = commentList.map(r => {
        const item = { momentId, roleId: r.RoleId, roleName: roleMap.get(r.RoleId)?.RoleName || "", action: "评论", createTime: new Date(r.CreateTime), text: r.Text };
        return item;
    });
    return {
        maxId: _.last(commentList)?.ID || 0,
        list: commentActionList,
    };
}
async function getForwardActionList(momentId, offset) {
    const query = models_1.MomentForwardModel.scope().where("OriginId", momentId).orderBy("ID", "ASC").where("ID", ">=", offset).limit(config_all_1.OAMExportLimit.forward).column(["MomentId", "ID"]);
    const forwardRelationList = await models_1.MomentForwardModel.executeByQuery(query);
    const forwardIdList = forwardRelationList.map(r => r.MomentId);
    const forwardMomentList = await models_1.MomentModel.find({ ID: forwardIdList, Status: 0 /* Statues.Normal */ }, { cols: ['ID', 'RoleId', 'Text', 'CreateTime'], limit: config_all_1.OAMExportLimit.forward });
    const roleIds = _.uniq(forwardMomentList.map(item => item.RoleId));
    const roleInfos = await RoleInfos_1.RoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName"] });
    const roleMap = (0, util_1.keyToRecordMap)(roleInfos, "RoleId");
    const forwardActionList = forwardMomentList.map(r => {
        const item = { momentId: r.ID, roleId: r.RoleId, roleName: roleMap.get(r.RoleId)?.RoleName || "", action: "转发", createTime: new Date(r.CreateTime), text: r.Text };
        return item;
    });
    return {
        maxId: _.last(forwardRelationList)?.ID || 0,
        list: forwardActionList,
    };
}
async function getLikeActionList(momentId, offset) {
    const query = models_1.MomentLikeModel.normalScope().where("MomentId", momentId).where("Status", 0 /* Statues.Normal */).where("ID", ">", offset).orderBy("ID", "ASC").limit(config_all_1.OAMExportLimit.like).columns(["RoleId", "CreateTime", "ID"]);
    const likeList = await models_1.MomentLikeModel.executeByQuery(query);
    const roleIds = _.uniq(likeList.map(item => item.RoleId));
    const roleInfos = await RoleInfos_1.RoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName"] });
    const roleMap = (0, util_1.keyToRecordMap)(roleInfos, "RoleId");
    const likeActionList = likeList.map(r => {
        const item = { momentId, roleId: r.RoleId, roleName: roleMap.get(r.RoleId)?.RoleName || "", action: "点赞", createTime: new Date(r.CreateTime), text: "" };
        return item;
    });
    return {
        maxId: _.last(likeList)?.ID || 0,
        list: likeActionList,
    };
}
//# sourceMappingURL=service.js.map
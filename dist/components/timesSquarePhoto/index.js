"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimesSquarePhotoComponent = exports.paths = void 0;
const injectIpHelper_1 = require("../../middlewares/injectIpHelper");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/times_square_photo/list",
        paramsSchema: type_1.ReqSchemas.TimesSquarePhotoList,
        operation: operation_1.timesSquarePhotoList,
    },
    {
        method: "post",
        url: "/times_square_photo/add",
        paramsSchema: type_1.ReqSchemas.TimesSquarePhotoAdd,
        before: (0, injectIpHelper_1.injectIpHelper)(),
        operation: operation_1.timesSquarePhotoAdd,
    },
    {
        method: "post",
        url: "/times_square_photo/del",
        paramsSchema: type_1.ReqSchemas.TimesSquarePhotoDel,
        operation: operation_1.timesSquarePhotoDel,
    },
];
exports.TimesSquarePhotoComponent = {
    paths: exports.paths,
    prefix: "/times_square_photo/",
};
//# sourceMappingURL=index.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    TimesSquarePhotoList: {
        type: "object",
        properties: {
            roleid: { type: "number", minimum: 0 },
            page: { type: "number", default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10 },
        },
        required: ["roleid"],
    },
    TimesSquarePhotoAdd: {
        type: "object",
        properties: {
            roleid: { type: "number", minimum: 0 },
            url: {
                type: "string",
                minLength: 1,
                transform: ["trim"],
            },
            index: { type: "number" },
        },
        required: ["roleid", "url", "index"],
    },
    TimesSquarePhotoDel: {
        type: "object",
        properties: { roleid: { type: "number", minimum: 0 }, index: { type: "number" } },
        required: ["roleid", "index"],
    },
};
//# sourceMappingURL=type.js.map
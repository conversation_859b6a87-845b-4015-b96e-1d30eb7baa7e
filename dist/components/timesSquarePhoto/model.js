"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimesSquarePhotoModel = exports.TimesSquarePhotoCols = void 0;
const BaseModel2_1 = require("../../models/BaseModel2");
exports.TimesSquarePhotoCols = [
    "id",
    "roleId",
    "index",
    "url",
    "status",
    "auditStatus",
    "createTime",
];
class TimesSquarePhotoModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_times_square_photo");
    }
}
exports.TimesSquarePhotoModel = new TimesSquarePhotoModelClass();
//# sourceMappingURL=model.js.map
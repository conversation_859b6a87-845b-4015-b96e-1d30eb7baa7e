"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatPhotoUrl = formatPhotoUrl;
exports.timesSquarePhotoList = timesSquarePhotoList;
exports.timesSquarePhotoAdd = timesSquarePhotoAdd;
exports.timesSquarePhotoDel = timesSquarePhotoDel;
const config_all_1 = require("../../common/config.all");
const constants_1 = require("../../common/constants");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const imageAudit_1 = require("../../services/imageAudit");
const sendImageAudit_1 = require("../../services/sendImageAudit");
const model_1 = require("./model");
const _ = require("lodash");
function formatPhotoUrl(url) {
    if (url && url.startsWith("http")) {
        return url.replace(/(https?:\/\/)nos\.netease\.com\/([0-9a-zA-Z]+)\/(.*)/, "$1$2.nosdn.127.net/$3");
    }
    return url;
}
/** 列出玩家时代广场图片 */
async function timesSquarePhotoList(params) {
    const viewerId = params.roleid;
    const ret = await model_1.TimesSquarePhotoModel.powerListQuery({
        initQuery: model_1.TimesSquarePhotoModel.normalScope(),
        where: { roleId: viewerId },
        orderBy: [["index"], ["asc"]],
        pagination: { page: params.page, pageSize: params.pageSize },
        select: ["roleId", "url", "auditStatus", "index", "createTime"],
    });
    for (const e of ret.list) {
        e.url = formatPhotoUrl(e.url);
    }
    return { list: ret.list, count: ret.meta.totalCount };
}
/** 同一个index上传url会覆盖， 游戏自己控制允许的数量，通过限制index的最大值 */
async function timesSquarePhotoAdd(params) {
    const url = (0, util_1.trim)(params.url);
    const indexRange = [1, config_all_1.timesSquarePhotoCfg.maxSizePerRoleId];
    if (!(0, util_1.isNosUrl)(url)) {
        throw errorCodes_1.TimesSquarePhotoErrors.InvalidPhotoUrl;
    }
    if (params.index < indexRange[0] || params.index > indexRange[1]) {
        throw errorCodes_1.TimesSquarePhotoErrors.OutOufIndexRange;
    }
    const addProps = {
        roleId: params.roleid,
        index: params.index,
        url: params.url,
        status: 0 /* Statues.Normal */,
        createTime: Date.now(),
        auditStatus: constants_1.AuditStatues.Auditing,
    };
    await model_1.TimesSquarePhotoModel.createOrUpdate(addProps, _.omitBy(addProps, ["roleId", "index"]));
    if (params.url) {
        const picId = (0, imageAudit_1.genPicIdFromInfo)({ id: "" + params.index, type: "times_square_photo" });
        (0, sendImageAudit_1.sendPic)([params.url], { roleId: params.roleid, picId: picId, media: constants_1.EPicMediaType.Image, ip: params.ip });
    }
    const data = {
        auditStatus: addProps.auditStatus,
        createTime: addProps.createTime,
    };
    return data;
}
/** 删除玩家时代广场个人图片 */
async function timesSquarePhotoDel(params) {
    const ret = await model_1.TimesSquarePhotoModel.updateByCondition({ roleId: params.roleid, index: params.index, status: 0 /* Statues.Normal */ }, { status: -1 /* Statues.Deleted */ });
    const data = {
        isOk: ret.affectedRows > 0,
    };
    return data;
}
//# sourceMappingURL=operation.js.map
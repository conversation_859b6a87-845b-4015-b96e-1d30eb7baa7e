"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CertifiedPlayerMaxNum = exports.MaxHandPickWallSize = exports.OpenHandPickLimit = exports.MaxAtRoleIdSize = exports.MaxPhotosWallPerGuild = exports.MaxGuildLevel = exports.MinGuildLevel = exports.GuildPhotoWallTable = exports.MaxPhotosSizePerWall = exports.HandPickWallId = void 0;
exports.HandPickWallId = "handpick";
exports.MaxPhotosSizePerWall = 50;
exports.GuildPhotoWallTable = [
    { guildLevel: 1, newTemplate: 1, quality: 'low', wallLimit: 1, photoLimit: 8 },
    { guildLevel: 2, newTemplate: 0, quality: 'low', wallLimit: 1, photoLimit: 8 },
    { guildLevel: 3, newTemplate: 1, quality: 'low', wallLimit: 2, photoLimit: 16 },
    { guildLevel: 4, newTemplate: 1, quality: 'low', wallLimit: 3, photoLimit: 24 },
    { guildLevel: 5, newTemplate: 1, quality: 'medium', wallLimit: 3, photoLimit: 30 },
    { guildLevel: 6, newTemplate: 1, quality: 'medium', wallLimit: 3, photoLimit: 30 },
    { guildLevel: 7, newTemplate: 1, quality: 'high', wallLimit: 3, photoLimit: 36 },
];
exports.MinGuildLevel = 1;
exports.MaxGuildLevel = 7;
exports.MaxPhotosWallPerGuild = 3;
exports.MaxAtRoleIdSize = 10;
exports.OpenHandPickLimit = {
    MinGuildLevel: 5,
    MinPhotoCnt: 30
};
exports.MaxHandPickWallSize = 1;
exports.CertifiedPlayerMaxNum = 1000;
//# sourceMappingURL=constant.js.map
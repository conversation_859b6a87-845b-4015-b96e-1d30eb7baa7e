"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    GuildPhotoWallList: {
        roleid: { type: Number },
        guildId: { type: Number },
    },
    GuildPhotoWallPhotoIdShow: {
        roleid: { type: Number },
        id: { type: Number },
    },
    GuildPhotoWallPhotoIdMove: {
        roleid: { type: Number },
        id: { type: Number },
        slot: { type: Number },
    },
    GuildPhotoWallHandpickWallUp: {
        roleid: { type: Number },
        photoId: { type: Number },
        wallId: { type: Number },
        slot: { type: Number },
    },
    GuildPhotoWallHandpickWallDown: {
        roleid: { type: Number },
        wallId: { type: Number },
        slot: { type: Number },
    },
    GuildPhotoWallPhotoIdDel: {
        roleid: { type: Number },
        isLeader: { type: Boolean },
        id: { type: Number },
    },
    GuildPhotoWallPhotoIdLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    GuildPhotoWallPhotoIdCancelLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    GuildPhotoWallCommentIdLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    GuildPhotoWallCommentIdCancelLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    GuildPhotoWallPhotoIdCommentList: {
        roleid: { type: Number },
        id: { type: Number },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    GuildPhotoWallPhotoIdCommentAdd: {
        roleid: { type: Number },
        id: { type: Number },
        text: { type: String },
        replyId: { type: Number, required: false },
    },
    GuildPhotoWallPhotoPhotoIdCommentDel: {
        roleid: { type: Number },
        isLeader: { type: Boolean },
        id: { type: Number },
    },
    GuildPhotoWallIdPhotoAdd: {
        properties: {
            roleid: { type: 'number' },
            id: { type: 'number' },
            overwrite: { type: 'boolean', default: true },
            text: { type: 'string', },
            url: { type: 'string' },
            slot: { type: 'number' },
            atRoleIds: { type: 'array', items: { type: 'number' } },
            canComment: { type: 'number', default: 1 }
        },
        required: ['roleid', 'id', 'url', 'slot']
        // roleid: { type: Number },
        // id: { type: Number },
        // overwrite: { type: Boolean, required: false, default: true },
        // text: { type: String, required: false },
        // url: { type: String },
        // slot: { type: Number },
        // atRoleIds: { type: Array, required: false, items: { type: Number } },
        // canComment: { type: Number }
    },
    GuildPhotoWallIdShow: {
        roleid: { type: Number },
        id: { type: Number },
    },
    GuildPhotoWallIdUpdate: {
        roleid: { type: Number },
        id: { type: Number },
        name: { type: String, required: false },
        templateId: { type: Number, required: false },
        frameId: { type: Number, required: false },
        type: { type: Number, required: false, values: [0, 1] },
    },
    GuildPhotoWallAdd: {
        roleid: { type: Number },
        guildId: { type: Number },
        guildLevel: { type: Number },
        name: { type: String, required: false },
        templateId: { type: Number, required: false },
        frameId: { type: Number, required: false },
        type: { type: Number, required: false, values: [0, 1] },
    },
    GuildPhotoWallNotificationsList: {
        roleid: { type: Number },
        status: { type: Number, required: false, values: [0, 1] },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    GuildPhotoWallNotificationsRead: {
        roleid: { type: Number },
        notificationId: { type: Number },
    },
    GuildPhotoWallNotificationsReadAll: {
        roleid: { type: Number },
    },
};
//# sourceMappingURL=type.js.map
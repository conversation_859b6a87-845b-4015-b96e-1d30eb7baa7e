"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuildPhotoWallComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/guild/photo_wall/list",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallList,
        operation: operation_1.guildPhotoWallList,
    },
    {
        method: "get",
        url: "/guild/photo_wall/photo/:id/show",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoIdShow,
        operation: operation_1.guildPhotoWallPhotoIdShow,
    },
    {
        method: "post",
        url: "/guild/photo_wall/photo/:id/del",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoPhotoIdCommentDel,
        operation: operation_1.guildPhotoWallPhotoIdDel,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/guild/photo_wall/photo/:id/move",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoIdMove,
        operation: operation_1.guildPhotoWallPhotoIdMove,
    },
    {
        method: "post",
        url: "/guild/photo_wall/handpick_wall/up",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallHandpickWallUp,
        operation: operation_1.guildPhotoWallHandpickWallUp,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/guild/photo_wall/handpick_wall/down",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallHandpickWallDown,
        operation: operation_1.guildPhotoWallHandpickWallDown,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/guild/photo_wall/photo/:id/like",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoIdLike,
        operation: operation_1.guildPhotoWallPhotoIdLike,
    },
    {
        method: "post",
        url: "/guild/photo_wall/photo/:id/cancel_like",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoIdCancelLike,
        operation: operation_1.guildPhotoWallPhotoIdCancelLike,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/guild/photo_wall/comment/:id/like",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallCommentIdLike,
        operation: operation_1.guildPhotoWallCommentIdLike,
    },
    {
        method: "post",
        url: "/guild/photo_wall/comment/:id/cancel_like",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallCommentIdCancelLike,
        operation: operation_1.guildPhotoWallCommentIdCancelLike,
    },
    {
        method: "get",
        url: "/guild/photo_wall/photo/:id/comment/list",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoIdCommentList,
        operation: operation_1.guildPhotoWallPhotoIdCommentList,
    },
    {
        method: "post",
        url: "/guild/photo_wall/photo/:id/comment/add",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoIdCommentAdd,
        operation: operation_1.guildPhotoWallPhotoIdCommentAdd,
    },
    {
        method: "post",
        url: "/guild/photo_wall/photo/:photo_id/comment/del",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallPhotoPhotoIdCommentDel,
        operation: operation_1.guildPhotoWallPhotoIdCommentDel,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/guild/photo_wall/:id/photo/add",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallIdPhotoAdd,
        operation: operation_1.guildPhotoWallIdPhotoAdd,
        option: { validateBy: 'ajv' }
    },
    {
        method: "get",
        url: "/guild/photo_wall/:id/show",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallIdShow,
        operation: operation_1.guildPhotoWallIdShow,
    },
    {
        method: "post",
        url: "/guild/photo_wall/:id/update",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallIdUpdate,
        operation: operation_1.guildPhotoWallIdUpdate,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/guild/photo_wall/add",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallAdd,
        operation: operation_1.guildPhotoWallAdd,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/guild/photo_wall/notifications/list",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallNotificationsList,
        operation: operation_1.guildPhotoWallNotificationsList,
    },
    {
        method: "post",
        url: "/guild/photo_wall/notifications/read",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallNotificationsRead,
        operation: operation_1.guildPhotoWallNotificationsRead,
    },
    {
        method: "post",
        url: "/guild/photo_wall/notifications/read_all",
        paramsSchema: type_1.ReqSchemas.GuildPhotoWallNotificationsReadAll,
        operation: operation_1.guildPhotoWallNotificationsReadAll,
    },
];
exports.GuildPhotoWallComponent = {
    paths: exports.paths,
    prefix: "/guild/photo_wall/",
};
//# sourceMappingURL=index.js.map
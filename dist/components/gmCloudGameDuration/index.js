"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmCloudGameDurationComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/gm/cloud_game_duration/expire_daily_duration",
        paramsSchema: type_1.ReqSchemas.GmCloudGameDurationExpireDailyDuration,
        operation: operation_1.gmCloudGameDurationExpireDailyDuration,
    },
    {
        method: "post",
        url: "/gm/cloud_game_duration/month_card/merge_server",
        paramsSchema: type_1.ReqSchemas.GmCloudGameDurationMonthCardMergeServer,
        operation: operation_1.gmCloudGameDurationMonthCardMergeServer,
    },
];
exports.GmCloudGameDurationComponent = {
    paths: exports.paths,
    prefix: "/gm/cloud_game_duration/",
};
//# sourceMappingURL=index.js.map
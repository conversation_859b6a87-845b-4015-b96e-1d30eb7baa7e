"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processMergeEvent = processMergeEvent;
const logger_1 = require("../../logger");
const monthCardAccount_1 = require("../../models/monthCardAccount");
const monthCardChangeLog_1 = require("../../models/monthCardChangeLog");
const _ = require("lodash");
const logger = logger_1.cloudGameLogger.child({ clazz: "mergeMonthCard" });
async function migrateMonthCard(r, mergeEvent) {
    let result = { id: r.id, urs: r.urs, success: false, action: null, failMsg: "", from: r.serverId, to: 0 };
    logger.info(r, "MigrateMonthCardStart");
    const merge = mergeEvent.merges.find(m => m.from === r.serverId);
    if (!merge)
        return;
    const serverId = merge.to;
    result.to = merge.to;
    const now = Date.now();
    const urs = r.urs;
    const duration = r.expireAt - Math.floor((new Date(mergeEvent.time)).getTime() / 1000);
    const curAccount = await monthCardAccount_1.MonthCardAccountModel.findOne({ urs: urs, serverId: serverId });
    const mergeId = JSON.stringify([mergeEvent.time, r.serverId, serverId]);
    const changeRecord = await monthCardChangeLog_1.MonthCardChangeLogModel.findOne({ urs, payload: mergeId });
    if (changeRecord && changeRecord.id) {
        result = { ...result, success: false, failMsg: "AlreadyMigrate" };
        logger.warn({ r, newServerId: serverId, duration }, "AlreadyMigrate");
    }
    else {
        if (curAccount && curAccount.id) {
            if (curAccount.expireAt * 1000 >= now) { // 目前月卡还未过期的情况, 直接延长过期时间
                const expireAt = curAccount.expireAt + duration;
                const upProps = {
                    updateTime: now,
                    expireAt
                };
                const upRet = await monthCardAccount_1.MonthCardAccountModel.updateById(curAccount.id, upProps);
                const cgProps = {
                    urs: urs,
                    createTime: now,
                    serverId: serverId,
                    reason: 2 /* MonthCardChangeReason.MERGE_SERVER */,
                    duration: duration,
                    payload: mergeId
                };
                const logId = await monthCardChangeLog_1.MonthCardChangeLogModel.insert(cgProps);
                result = { ...result, success: true, action: "transfer" };
                logger.info({ urs, serverId, upProps, upRet, logId }, "ExtendMonthCard");
            }
            else {
                result = { ...result, success: false, failMsg: "AlreadyExpired" };
                logger.info({ urs, serverId }, "AlreadyExpired");
            }
        }
        else {
            const expireAt = r.expireAt;
            const props = {
                urs: urs,
                createTime: now,
                updateTime: now,
                serverId,
                expireAt
            };
            const accountId = await monthCardAccount_1.MonthCardAccountModel.insert(props);
            const cgProps = {
                urs: urs,
                createTime: now,
                serverId: serverId,
                reason: 2 /* MonthCardChangeReason.MERGE_SERVER */,
                duration: duration,
                payload: mergeId
            };
            const logId = await monthCardChangeLog_1.MonthCardChangeLogModel.insert(cgProps);
            result = { ...result, success: true, action: "transfer" };
            logger.info({ props, accountId, logId }, "InitMonthCardAccount");
        }
    }
    logger.info(r, "MigrateMonthCardEnd");
    return result;
}
async function processMergeEvent(mergeEvent) {
    const fromServerIds = _.uniq(mergeEvent.merges.map(r => r.from));
    let query = monthCardAccount_1.MonthCardAccountModel.scope()
        .whereIn('serverId', fromServerIds)
        .where('expireAt', '>', Math.floor((new Date(mergeEvent.time).getTime()) / 1000));
    let records = await monthCardAccount_1.MonthCardAccountModel.executeByQuery(query);
    let list = [];
    for (let r of records) {
        const result = await migrateMonthCard(r, mergeEvent);
        list.push(result);
    }
    return { list, mergeEvent };
}
//# sourceMappingURL=service.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmCloudGameDurationExpireDailyDuration = gmCloudGameDurationExpireDailyDuration;
exports.gmCloudGameDurationMonthCardMergeServer = gmCloudGameDurationMonthCardMergeServer;
exports.gmCloudGameDurationMonthCardQueryWeb = gmCloudGameDurationMonthCardQueryWeb;
exports.gmCloudGameDurationMonthCardShow = gmCloudGameDurationMonthCardShow;
exports.gmCloudGameDurationMonthCardServerIds = gmCloudGameDurationMonthCardServerIds;
exports.gmCloudGameDurationMonthCardChargeList = gmCloudGameDurationMonthCardChargeList;
const _ = require("lodash");
const errorCodes_1 = require("../../errorCodes");
const service_1 = require("../cloudGameDuration/service");
const service_2 = require("./service");
const fs = require("fs");
const path = require("path");
const helper_1 = require("../../helper");
const monthCardAccount_1 = require("../../models/monthCardAccount");
const monthCardCharge_1 = require("../../models/monthCardCharge");
/** 用来模拟当日时长第二天凌晨会过期的规则 */
async function gmCloudGameDurationExpireDailyDuration(params) {
    const { ds } = params;
    const date = new Date(ds);
    const changeNum = await (0, service_1.dailyDurationExpire)(date);
    const data = { changeNum, ds };
    return data;
}
/** 云游戏月卡时长需要合并 */
async function gmCloudGameDurationMonthCardMergeServer(params) {
    if (!(params.time && params.time.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/))) {
        throw errorCodes_1.BaseErrors.InvalidArgument;
    }
    if (params.merges) {
        for (let m of params.merges) {
            let from = parseInt("" + m.from, 10);
            if (!_.isInteger(from)) {
                throw errorCodes_1.BaseErrors.InvalidArgument;
            }
            m.from = from;
            let to = parseInt("" + m.to, 10);
            if (!_.isInteger(to)) {
                throw errorCodes_1.BaseErrors.InvalidArgument;
            }
            m.to = to;
            if (m.from === m.to) {
                throw errorCodes_1.CloudGameDurationErrors.MergeServerNotBeSame;
            }
        }
    }
    const data = await (0, service_2.processMergeEvent)({ time: params.time, merges: params.merges });
    return data;
}
async function gmCloudGameDurationMonthCardQueryWeb(req, res, next) {
    const html = fs.readFileSync(path.join(__dirname, "../../../public/cloud_game_duration/month_card_web_ui.html"));
    res.end(html);
}
async function gmCloudGameDurationMonthCardShow(req, res, next) {
    try {
        const { urs, serverId } = req.query;
        if (!urs) {
            throw errorCodes_1.BaseErrors.InvalidArgument;
        }
        let initQuery = monthCardAccount_1.MonthCardAccountModel.scope().where({ urs });
        if (serverId > 0) {
            initQuery = initQuery.where({
                serverId
            });
        }
        const data = await monthCardAccount_1.MonthCardAccountModel.powerQuery({
            initQuery,
            orderBy: [['createTime'], ['desc']],
            pagination: {
                page: 1,
                pageSize: 100
            },
            select: ['createTime', 'expireAt', 'id', 'urs', 'serverId']
        });
        const list = data.map(item => {
            return {
                createTime: item.createTime,
                expireAt: item.expireAt,
                serverId: item.serverId,
                status: monthCardAccount_1.MonthCardAccountModel.getMonthCardStatus(item.expireAt),
                id: item.id,
            };
        });
        res.send({ code: 0, data: list });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function gmCloudGameDurationMonthCardServerIds(req, res, next) {
    let query = monthCardAccount_1.MonthCardAccountModel.scope().select('serverId').distinct();
    const data = await monthCardAccount_1.MonthCardAccountModel.executeByQuery(query);
    const serverIds = _.uniq(data.map(item => item.serverId));
    res.send({ code: 0, data: serverIds });
}
async function gmCloudGameDurationMonthCardChargeList(req, res, next) {
    try {
        const { urs, startTime, endTime, orderId } = req.query;
        if (!urs && !orderId) {
            throw new Error("urs or orderId is required");
        }
        let query = monthCardCharge_1.MonthCardChargeModel.scope().orderBy('id', 'desc');
        if (urs) {
            query = query.where('urs', urs);
        }
        if (orderId) {
            query = query.where('orderId', orderId);
        }
        if (startTime > 0 && endTime > 0 && startTime < endTime) {
            query = query.where('buyTime', '>=', startTime).where('buyTime', '<=', endTime);
        }
        const rows = await monthCardCharge_1.MonthCardChargeModel.powerQuery({
            initQuery: query,
            pagination: {
                page: 1,
                pageSize: 100
            },
            select: monthCardCharge_1.MonthCardChargeCols
        });
        const list = rows.map(item => {
            return {
                id: item.id,
                duration: item.duration,
                orderId: item.orderId,
                channel: item.channel,
                serverId: item.serverId,
                urs: item.urs,
                buyTime: item.buyTime,
            };
        });
        res.send({ code: 0, data: list });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=operation.js.map
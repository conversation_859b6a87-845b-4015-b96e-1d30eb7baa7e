"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmClubCleanByServer = gmClubCleanByServer;
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const logger = (0, logger_1.clazzLogger)("gmClub/operation");
/** 根据服务器id批量清理俱乐部相关数据 */
async function gmClubCleanByServer(params) {
    const rows = await models_1.ClubModel.find({ ServerId: params.serverId }, { limit: 100 });
    if (rows && rows.length > 0) {
        logger.info({ rows, params }, "PrepareCleanClub");
    }
    const ret = await models_1.ClubModel.deleteByCondition({ ServerId: params.serverId });
    logger.info({ ret, params }, "ClubCleanByServer");
    const data = { cleanNum: ret.affectedRows, serverId: params.serverId };
    return data;
}
//# sourceMappingURL=operation.js.map
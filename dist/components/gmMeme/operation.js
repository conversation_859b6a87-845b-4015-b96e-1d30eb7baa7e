"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmMemeList = gmMemeList;
exports.gmMemeAudit = gmMemeAudit;
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const model_1 = require("../meme/model");
const logger = (0, logger_1.clazzLogger)("gm.meme.operation");
/** 查看某一个玩家的列表 */
async function gmMemeList(params) {
    const rows = await model_1.MemeModel.find({ roleId: params.roleId }, { cols: ["id", "roleId", "url", "auditStatus", "createTime"] });
    const data = {
        list: rows,
    };
    return data;
}
/** 查看某一个玩家的列表 */
async function gmMemeAudit(params) {
    const r = await model_1.MemeModel.findOne({ url: params.url, roleId: params.roleId });
    if (!r) {
        throw errorCodes_1.errorCode.MemeNotFound;
    }
    const upRet = await model_1.MemeModel.updateByCondition({ id: r.id }, { auditStatus: params.auditStatus });
    logger.info({ meme: r, upRet }, "gmMemeAuditSetOk");
    const data = {
        id: r.id,
        roleId: r.roleId,
        url: r.url,
        auditStatus: params.auditStatus,
    };
    return data;
}
//# sourceMappingURL=operation.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillComboComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/skill_combo/add",
        paramsSchema: type_1.ReqSchemas.SkillComboAdd,
        operation: operation_1.skillComboAdd,
    },
    {
        method: "post",
        url: "/skill_combo/del",
        paramsSchema: type_1.ReqSchemas.SkillComboDel,
        operation: operation_1.skillComboDel,
    },
    {
        method: "get",
        url: "/skill_combo/get",
        paramsSchema: type_1.ReqSchemas.SkillComboGet,
        operation: operation_1.skillComboGet,
    },
    {
        method: "post",
        url: "/skill_combo/collect",
        paramsSchema: type_1.ReqSchemas.SkillComboCollect,
        operation: operation_1.skillComboCollect,
    },
    {
        method: "post",
        url: "/skill_combo/cancel_collect",
        paramsSchema: type_1.ReqSchemas.SkillComboCancelCollect,
        operation: operation_1.skillComboCancelCollect,
    },
    {
        method: "post",
        url: "/skill_combo/like",
        paramsSchema: type_1.ReqSchemas.SkillComboLike,
        operation: operation_1.skillComboLike,
    },
    {
        method: "post",
        url: "/skill_combo/cancel_like",
        paramsSchema: type_1.ReqSchemas.SkillComboCancelLike,
        operation: operation_1.skillComboCancelLike,
    },
    {
        method: "post",
        url: "/skill_combo/sync",
        paramsSchema: type_1.ReqSchemas.SkillComboSync,
        operation: operation_1.skillComboSync,
    },
    {
        method: "post",
        url: "/skill_combo/update",
        paramsSchema: type_1.ReqSchemas.SkillComboUpdate,
        operation: operation_1.skillComboUpdate,
    },
    {
        method: "get",
        url: "/skill_combo/list",
        paramsSchema: type_1.ReqSchemas.SkillComboList,
        operation: operation_1.skillComboList,
    },
];
exports.SkillComboComponent = {
    paths: exports.paths,
    prefix: "/skill_combo/",
};
//# sourceMappingURL=index.js.map
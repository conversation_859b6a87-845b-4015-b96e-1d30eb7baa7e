"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillComboPreDelTimeRecorder = void 0;
exports.getLastDeleteTime = getLastDeleteTime;
exports.setLastDeleteTime = setLastDeleteTime;
exports.skillComboAdd = skillComboAdd;
exports.skillComboDel = skillComboDel;
exports.skillComboGet = skillComboGet;
exports.skillComboCollect = skillComboCollect;
exports.skillComboCancelCollect = skillComboCancelCollect;
exports.skillComboLike = skillComboLike;
exports.skillComboCancelLike = skillComboCancelLike;
exports.skillComboSync = skillComboSync;
exports.skillComboUpdate = skillComboUpdate;
exports.skillComboList = skillComboList;
const _ = require("lodash");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const skillCombo_1 = require("./models/skillCombo");
const skillComboCollect_1 = require("./models/skillComboCollect");
const skillComboLike_1 = require("./models/skillComboLike");
const config_1 = require("../../common/config");
const skillComboSync_1 = require("./models/skillComboSync");
const RoleInfos_1 = require("../../models/RoleInfos");
const audit_1 = require("../neDun/audit");
const redis_1 = require("../../common/redis");
const dateUtil_1 = require("../../common/dateUtil");
const logger = (0, logger_1.clazzLogger)("skillCombo/operation");
function getLastDeleteTime() { }
function setLastDeleteTime() { }
class SkillComboPreDelTimeRecorder {
    constructor(roleId) {
        this.roleId = roleId;
    }
    getKey() {
        return `nsh_skill_combo_pre_del_time_${this.roleId}`;
    }
    async getPreDelTime() {
        const ret = await (0, redis_1.getRedis)().getAsync(this.getKey());
        if (ret) {
            return parseInt(ret, 10);
        }
        return 0;
    }
    setDelTime(roleId, ts) {
        return (0, redis_1.getRedis)().setAsync(this.getKey(), ts, redis_1.ExpireType.EX, SkillComboPreDelTimeRecorder.WAIT_SECONDS_AFTER_DEL * 2);
    }
    static create(roleId) {
        return new SkillComboPreDelTimeRecorder(roleId);
    }
    async isPlayerAllowUpload(ts) {
        const preDelTime = await this.getPreDelTime();
        if (preDelTime > 0) {
            return ts - preDelTime > SkillComboPreDelTimeRecorder.WAIT_SECONDS_AFTER_DEL;
        }
        else {
            return true;
        }
    }
    async getDurationForNextUpload(ts) {
        const preDelTime = await this.getPreDelTime();
        return Math.max(0, preDelTime + SkillComboPreDelTimeRecorder.WAIT_SECONDS_AFTER_DEL * 1000 - ts);
    }
}
exports.SkillComboPreDelTimeRecorder = SkillComboPreDelTimeRecorder;
SkillComboPreDelTimeRecorder.WAIT_SECONDS_AFTER_DEL = config_1.skillComboCfg.waitSecondsAfterDel;
/** 上传技能组合 */
async function skillComboAdd(params) {
    const tagIds = params.tagIds.join(",");
    const roleId = params.roleid;
    const durationForUpload = await SkillComboPreDelTimeRecorder.create(roleId).getDurationForNextUpload(Date.now());
    if (durationForUpload > 0) {
        const message = errorCodes_1.SkillComboErrors.NeedWaitTimeForUpload.msg.replace("{{duration}}", (0, dateUtil_1.formatDuration)(durationForUpload));
        await (0, errorCodes_1.BussError)(errorCodes_1.SkillComboErrors.NeedWaitTimeForUpload, message);
    }
    const hasUploadSameCategorySameJob = await skillCombo_1.SkillComboModel.hasUploadSameCategorySameJob(roleId, params.category, params.jobId);
    if (hasUploadSameCategorySameJob) {
        throw errorCodes_1.SkillComboErrors.AlreadyUploadSameCategorySameJob;
    }
    await (0, audit_1.textCheckFilter)({ content: [params.name, params.desc].join(""), title: "", userId: params.roleId });
    let jobId = params.jobId;
    if (!jobId) {
        const roleInfo = await RoleInfos_1.RoleInfoModel.findOne({ RoleId: roleId }, ["RoleId", "JobId"]);
        jobId = roleInfo?.JobId || 0;
    }
    const addProp = {
        roleId: params.roleid,
        url: params.url,
        name: params.name,
        desc: params.desc,
        jobId,
        category: params.category,
        region: params.region,
        tagIds: tagIds,
    };
    const id = await skillCombo_1.SkillComboModel.insert(addProp);
    return { id: id };
}
/** 删除技能组合 */
async function skillComboDel(params) {
    const id = params.id;
    const roleId = params.roleid;
    const record = await skillCombo_1.SkillComboModel.checkIsOwnRecord(id, roleId);
    const ret = await skillCombo_1.SkillComboModel.softDeleteByCondition({ id, roleId });
    logger.info({ params, ret, record }, "SkillComboDel");
    const isOk = ret.affectedRows > 0;
    if (isOk) {
        SkillComboPreDelTimeRecorder.create(roleId).setDelTime(roleId, Date.now());
    }
    const data = {
        isOk,
    };
    return data;
}
/** 获取技能组合 */
async function skillComboGet(params) {
    const r = await skillCombo_1.SkillComboModel.checkExist(params.id);
    const list = await formatSkillComboList([r], params.roleid);
    const data = list[0];
    return data;
}
/** 收藏技能组合 */
async function skillComboCollect(params) {
    const roleId = params.roleid;
    const skillComboId = params.id;
    const skillCombo = await skillCombo_1.SkillComboModel.checkExist(skillComboId);
    const collectCntWithJob = await skillComboCollect_1.SkillComboCollectModel.getCollectCntPerCategoryPerJobId(roleId, skillCombo.category, skillCombo.jobId);
    if (collectCntWithJob >= config_1.skillComboCfg.maxCollectPerPlayerPerCategoryPerJob) {
        throw errorCodes_1.SkillComboErrors.OutOfCollectPerCategoryPerJobLimit;
    }
    const collect = await skillComboCollect_1.SkillComboCollectModel.findOne({ roleId, skillComboId }, ["id", "roleId", "status"]);
    let id = 0;
    if (!_.isEmpty(collect) && collect.status === 0 /* Statues.Normal */) {
        throw errorCodes_1.SkillComboErrors.AlreadyCollected;
    }
    if (!_.isEmpty(collect)) {
        id = collect.id;
        await skillComboCollect_1.SkillComboCollectModel.updateById(collect.id, { status: 0 /* Statues.Normal */ });
    }
    else {
        const props = {
            roleId: params.roleid,
            skillComboId,
            category: skillCombo.category,
            targetId: skillCombo.roleId,
            jobId: skillCombo.jobId,
        };
        id = await skillComboCollect_1.SkillComboCollectModel.insert(props);
    }
    logger.info({ params, id }, "UserCollectSkillCombo");
    skillCombo_1.SkillComboModel.incrHot(skillComboId, "collectCnt");
    const data = {
        isOk: true,
    };
    return data;
}
/** 取消收藏技能组合 */
async function skillComboCancelCollect(params) {
    const skillComboId = params.id;
    const roleId = params.roleid;
    const skillCombo = await skillCombo_1.SkillComboModel.checkExist(skillComboId);
    const collect = await skillComboCollect_1.SkillComboCollectModel.findOne({ roleId, skillComboId }, ["id", "roleId", "status"]);
    if (_.isEmpty(collect) || collect.status === -1 /* Statues.Deleted */) {
        throw errorCodes_1.SkillComboErrors.NotCollected;
    }
    else {
        await skillComboCollect_1.SkillComboCollectModel.softDeleteById(collect.id);
        logger.debug({ params, id: collect.id, skillCombo }, "UserCancelCollectSkillCombo");
    }
    skillCombo_1.SkillComboModel.decrHot(skillComboId, "collectCnt");
    const data = {
        isOk: true,
    };
    return data;
}
/** 点赞技能组合 */
async function skillComboLike(params) {
    const roleId = params.roleid;
    const skillComboId = params.id;
    const skillCombo = await skillCombo_1.SkillComboModel.checkExist(skillComboId);
    const collect = await skillComboLike_1.SkillComboLikeModel.findOne({ roleId, skillComboId }, ["id", "roleId", "status"]);
    let id = 0;
    if (!_.isEmpty(collect) && collect.status === 0 /* Statues.Normal */) {
        throw errorCodes_1.SkillComboErrors.AlreadyLiked;
    }
    if (!_.isEmpty(collect)) {
        id = collect.id;
        await skillComboLike_1.SkillComboLikeModel.updateById(collect.id, { status: 0 /* Statues.Normal */ });
    }
    else {
        const props = {
            roleId: params.roleid,
            skillComboId,
            targetId: skillCombo.roleId,
        };
        id = await skillComboLike_1.SkillComboLikeModel.insert(props);
    }
    logger.debug({ params, id }, "UserLikeSkillCombo");
    skillCombo_1.SkillComboModel.incrHot(skillComboId, "likeCnt");
    const data = {
        isOk: true,
    };
    return data;
}
/** 取消点赞技能组合 */
async function skillComboCancelLike(params) {
    const skillComboId = params.id;
    const roleId = params.roleid;
    const skillCombo = await skillCombo_1.SkillComboModel.checkExist(skillComboId);
    const collect = await skillComboLike_1.SkillComboLikeModel.findOne({ roleId, skillComboId }, ["id", "roleId", "status"]);
    if (_.isEmpty(collect) || collect.status === -1 /* Statues.Deleted */) {
        throw errorCodes_1.SkillComboErrors.NotLiked;
    }
    else {
        await skillComboLike_1.SkillComboLikeModel.softDeleteById(collect.id);
        logger.debug({ params, id: collect.id, skillCombo }, "UserCancelLikeSkillCombo");
    }
    skillCombo_1.SkillComboModel.decrHot(skillComboId, "likeCnt");
    const data = {
        isOk: true,
    };
    return data;
}
/** 同步技能组合 */
async function skillComboSync(params) {
    const roleId = params.roleid;
    const skillComboId = params.id;
    const sync = await skillComboSync_1.SkillComboSyncModel.findOne({ roleId, skillComboId }, ["id", "roleId", "status"]);
    if (!_.isEmpty(sync) && sync.status === 0 /* Statues.Normal */) {
        //** 再次同步只是不触发计数*/
        logger.debug({ params, skillComboId }, "UserAlreadySyncCombo");
        return { isOk: false };
    }
    let id = 0;
    const skillCombo = await skillCombo_1.SkillComboModel.checkExist(skillComboId);
    if (!_.isEmpty(sync)) {
        id = sync.id;
        await skillComboSync_1.SkillComboSyncModel.updateById(sync.id, { status: 0 /* Statues.Normal */ });
    }
    else {
        const props = {
            roleId: params.roleid,
            skillComboId,
            targetId: skillCombo.roleId,
        };
        id = await skillComboSync_1.SkillComboSyncModel.insert(props);
    }
    logger.debug({ params, id }, "UserSyncSkillCombo");
    skillCombo_1.SkillComboModel.incrHot(skillComboId, "syncCnt");
    const data = {
        isOk: true,
    };
    return data;
}
/** 修改技能组合 */
async function skillComboUpdate(params) {
    const id = params.id;
    const roleId = params.roleid;
    const skillCombo = await skillCombo_1.SkillComboModel.checkExist(id);
    if (roleId !== skillCombo.roleId) {
        throw errorCodes_1.SkillComboErrors.NotOwner;
    }
    await (0, audit_1.textCheckFilter)({ content: [params.name, params.desc].join(""), title: "", userId: params.roleId });
    const upProps = {};
    if (params.url) {
        upProps.url = params.url;
    }
    if (params.name) {
        upProps.name = params.name;
    }
    if (params.desc) {
        upProps.desc = params.desc;
    }
    if (params.category) {
        upProps.category = params.category;
    }
    if (params.region) {
        upProps.region = params.region;
    }
    if (params.tagIds) {
        upProps.tagIds = params.tagIds.join(",");
    }
    if (Object.keys(upProps).length > 0) {
        logger.debug({ params, upProps }, "UserUpdateSkillCombo");
        await skillCombo_1.SkillComboModel.updateById(id, upProps);
    }
    const data = { id };
    return data;
}
/** 技能组合列表 */
async function skillComboList(params) {
    const roleId = params.roleid;
    let query = skillCombo_1.SkillComboModel.normalScope();
    if (params.type === 2 /* EListType.Collect */) {
        const collectIds = await skillComboCollect_1.SkillComboCollectModel.getCollectIds(roleId);
        query.whereIn("id", collectIds);
    }
    else if (params.type === 3 /* EListType.MySelf */) {
        query = query.where("roleId", roleId);
    }
    if (params.kw) {
        query = query.where("name", "like", `%${params.kw}%`);
    }
    if (params.category > 0) {
        query = query.where("category", params.category);
    }
    if (params.jobId >= 0) {
        query = query.where("jobId", params.jobId);
    }
    const orderCol = params.sort_by === "hot" ? "hot" : "likeCnt";
    const { list, meta } = await skillCombo_1.SkillComboModel.powerListQuery({
        initQuery: query,
        select: skillCombo_1.SkillComboCols,
        where: { region: params.region },
        orderBy: [[orderCol], ["desc"]],
        pagination: { page: params.page, pageSize: params.pageSize },
    });
    const showList = await formatSkillComboList(list, roleId);
    const data = { list: showList, count: meta.totalCount };
    return data;
}
async function formatSkillComboList(rows, roleId) {
    const ids = rows.map((r) => r.id);
    if (ids.length === 0)
        return [];
    const collectIds = await skillComboCollect_1.SkillComboCollectModel.filterByCollected(ids, roleId);
    const likeIds = await skillComboLike_1.SkillComboLikeModel.filterByLiked(ids, roleId);
    const roleIds = rows.map((r) => r.roleId);
    const roleInfos = await RoleInfos_1.RoleInfoModel.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName", "JobId", "Gender", "HeadPaintId", "BodyPaintId", 'SubGender'] });
    return rows.map((r) => {
        const roleInfo = roleInfos.find((e) => e.RoleId === r.roleId) || {
            RoleId: r.roleId,
            RoleName: "",
            JobId: 0,
            Gender: 0,
            SubGender: 0,
            HeadPaintId: 0,
            BodyPaintId: 0,
        };
        const data = Object.assign({}, skillCombo_1.SkillComboModel.formatRecord(r), {
            isLiked: likeIds.includes(r.id),
            isCollected: collectIds.includes(r.id),
            roleName: roleInfo.RoleName,
            jobId: roleInfo.JobId,
            gender: roleInfo.Gender,
            subGender: roleInfo.SubGender,
            headPaintId: roleInfo.HeadPaintId,
            bodyPaintId: roleInfo.BodyPaintId,
        });
        return data;
    });
}
//# sourceMappingURL=operation.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillComboSyncModel = exports.SkillComboSyncCols = void 0;
const BaseModel2_1 = require("../../../models/BaseModel2");
exports.SkillComboSyncCols = [
    "id",
    "roleId",
    "skillComboId",
    "targetId",
    "status",
    "createdAt",
    "updatedAt",
];
class SkillComboSyncModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_skill_combo_sync");
    }
}
exports.SkillComboSyncModel = new SkillComboSyncModelClass();
//# sourceMappingURL=skillComboSync.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillComboModel = exports.SkillComboCols = void 0;
const util_1 = require("../../../common/util");
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
const BaseModel2_1 = require("../../../models/BaseModel2");
const logger = (0, logger_1.clazzLogger)("models/skillCombo");
exports.SkillComboCols = [
    "id",
    "roleId",
    "url",
    "name",
    "desc",
    "tagIds",
    "status",
    "category",
    "region",
    "hot",
    "likeCnt",
    "collectCnt",
    "syncCnt",
    "createdAt",
    "updatedAt",
];
class SkillComboModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_skill_combo");
    }
    async checkIsOwnRecord(id, roleId) {
        const r = await this.findOne({ id, status: 0 /* Statues.Normal */, roleId });
        if (r && r.id) {
            return r;
        }
        else {
            throw errorCodes_1.SkillComboErrors.RecordNotFound;
        }
    }
    async getUploadCnt(roleId) {
        const cnt = await this.count({ roleId, status: 0 /* Statues.Normal */ });
        return cnt;
    }
    async hasUploadSameCategorySameJob(roleId, category, jobId) {
        const r = await this.findOne({ roleId, category, status: 0 /* Statues.Normal */, jobId }, ["id"]);
        return !!(r && r.id);
    }
    hotChangeTypeToVal(type) {
        //  热度 = 收藏 + 同步*0.5, 避免浮点数出现误差， 统一乘以10
        const hotDelta = type === "syncCnt" ? 5 : 10;
        return hotDelta;
    }
    async incrHot(id, col) {
        try {
            const hotDelta = this.hotChangeTypeToVal(col);
            const query = this.raw("update ?? set `hot` = `hot` + ??, ?? = ?? + 1 where id = ?", [
                this.tableName,
                hotDelta,
                col,
                col,
                id,
            ]);
            return this.executeByQuery(query);
        }
        catch (err) {
            logger.error({ err, id, col }, "incrSkillComboHotFailed");
        }
    }
    async decrHot(id, col) {
        try {
            const hotDelta = this.hotChangeTypeToVal(col);
            const query = this.raw("update ?? set `hot` = `hot` - ??, ?? = ?? - 1 where `hot` > ?? and ?? > 0 and id = ?", [
                this.tableName,
                hotDelta,
                col,
                col,
                hotDelta,
                col,
                id,
            ]);
            return this.executeByQuery(query);
        }
        catch (err) {
            logger.error({ err, id, col }, "decrSkillComboHotFailed");
        }
    }
    formatRecord(r) {
        return {
            id: r.id,
            roleId: r.roleId,
            url: r.url,
            name: r.name,
            tagIds: (0, util_1.csvStrToIntArray)(r.tagIds),
            desc: r.desc,
            category: r.category,
            region: r.region,
            likeCnt: r.likeCnt,
            hot: Math.ceil(r.hot / 10),
        };
    }
    async checkExist(id) {
        const r = await this.findOne({ id, status: 0 /* Statues.Normal */ });
        if (r && r.id) {
            return r;
        }
        else {
            throw errorCodes_1.SkillComboErrors.RecordNotFound;
        }
    }
}
exports.SkillComboModel = new SkillComboModelClass();
//# sourceMappingURL=skillCombo.js.map
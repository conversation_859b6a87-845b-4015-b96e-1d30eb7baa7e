"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillComboCollectModel = exports.SkillComboCollectCols = void 0;
const config_1 = require("../../../common/config");
const BaseModel2_1 = require("../../../models/BaseModel2");
const skillCombo_1 = require("./skillCombo");
exports.SkillComboCollectCols = [
    "id",
    "roleId",
    "skillComboId",
    "targetId",
    "status",
    "createdAt",
    "updatedAt",
];
class SkillComboCollectModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_skill_combo_collect");
    }
    async getCollectCnt(roleId) {
        const cnt = await this.count({ roleId, status: 0 /* Statues.Normal */ });
        return cnt;
    }
    async getCollectCntPerCategoryPerJobId(roleId, category, jobId) {
        const query = this.scope()
            .from(this.tableName + " as cc")
            .innerJoin(skillCombo_1.SkillComboModel.tableName + " as sc", "cc.skillComboId", "sc.id")
            .where("cc.status", 0 /* Statues.Normal */)
            .where("cc.roleId", roleId)
            .where("cc.category", category)
            .where("cc.jobId", jobId)
            .where("sc.status", 0 /* Statues.Normal */);
        const cnt = await this.countByQuery(query);
        return cnt;
    }
    async getCollectIds(roleId) {
        const rows = await this.powerQuery({
            initQuery: this.scope(),
            where: { roleId, status: 0 /* Statues.Normal */ },
            select: ["id", "skillComboId"],
            orderBy: [["id"], ["desc"]],
            pagination: { page: 1, pageSize: config_1.skillComboCfg.maxCollectPerPlayer },
        });
        return rows.map((r) => r.skillComboId);
    }
    async filterByCollected(skillComboIds, roleId) {
        const rows = await this.find({ skillComboId: skillComboIds, roleId, status: 0 /* Statues.Normal */ }, { cols: ["skillComboId"] });
        return rows.map((r) => r.skillComboId);
    }
}
exports.SkillComboCollectModel = new SkillComboCollectModelClass();
//# sourceMappingURL=skillComboCollect.js.map
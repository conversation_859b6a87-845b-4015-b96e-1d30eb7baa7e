"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillComboLikeModel = exports.SkillComboLikeCols = void 0;
const BaseModel2_1 = require("../../../models/BaseModel2");
exports.SkillComboLikeCols = ['id', 'roleId', 'skillComboId', 'targetId', 'status', 'createdAt', 'updatedAt'];
class SkillComboLikeModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_skill_combo_like");
    }
    async getLikeCnt(roleId) {
        const cnt = await this.count({ roleId, status: 0 /* Statues.Normal */ });
        return cnt;
    }
    async filterByLiked(skillComboIds, roleId) {
        const rows = await this.find({ skillComboId: skillComboIds, roleId, status: 0 /* Statues.Normal */ }, { cols: ["skillComboId"] });
        return rows.map((r) => r.skillComboId);
    }
}
exports.SkillComboLikeModel = new SkillComboLikeModelClass();
//# sourceMappingURL=skillComboLike.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    SkillComboAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            url: { type: "string" },
            name: { type: "string", maxLength: 7, minLength: 1, transform: ["trim"] },
            desc: { type: "string", minLength: 1, maxLength: 50, transform: ["trim"] },
            tagIds: { type: "array", items: { type: "number" } },
            category: { type: "number", enum: [0, 1, 2, 3] },
            region: { type: "number" },
        },
        required: ["roleid", "url", "name", "tagIds", "desc", "category", "region"],
    },
    SkillComboDel: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboGet: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboCollect: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboCancelCollect: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboLike: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboSync: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboCancelLike: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    SkillComboUpdate: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            id: { type: "number" },
            url: { type: "string" },
            name: { type: "string", maxLength: 7, minLength: 1, transform: ["trim"] },
            desc: { type: "string", minLength: 1, maxLength: 50, transform: ["trim"] },
            tagIds: { type: "array", items: { type: "number" } },
            category: { type: "number", enum: [0, 1, 2, 3] },
            region: { type: "number" },
        },
        required: ["roleid", "id"],
    },
    SkillComboList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            category: { type: "number", enum: [0, 1, 2, 3] },
            sort_by: { type: "string", enum: ["hot", "like"] },
            type: { type: "number", enum: [1, 2, 3] },
            region: { type: "number" },
            kw: { type: "string" },
            page: { type: "number", default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10 },
        },
        required: ["roleid", "sort_by", "type", "region"],
    },
};
//# sourceMappingURL=type.js.map
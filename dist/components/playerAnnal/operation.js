"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EAnnalAction = void 0;
exports.getScoreIndex = getScoreIndex;
exports.getPlayerAnnalEventList = getPlayerAnnalEventList;
exports.playerAnnalSync = playerAnnalSync;
const _ = require("lodash");
const models_1 = require("../../models");
const util_1 = require("../../common/util");
const serverTransfer_1 = require("../../services/serverTransfer");
const logger_1 = require("../../logger");
const redDot_1 = require("../../services/redDot");
const logger = (0, logger_1.clazzLogger)('playerAnnal/operation');
var EAnnalAction;
(function (EAnnalAction) {
    EAnnalAction[EAnnalAction["UpVote"] = 1] = "UpVote";
    EAnnalAction[EAnnalAction["DownVote"] = 2] = "DownVote";
})(EAnnalAction || (exports.EAnnalAction = EAnnalAction = {}));
function getScoreIndex(eventArgs) {
    // get the correct condIndex
    let scoreIndex = 0;
    if (eventArgs && eventArgs.condIndex) {
        // 游戏客户端傻乎乎的从1开始数数, 这里处理成index
        scoreIndex = Math.max(0, eventArgs.condIndex - 1);
    }
    return scoreIndex;
}
/** set roleId inPlace */
function formatPlayerIdsInEventArgs(eventArgs, map) {
    if (eventArgs) {
        if (eventArgs.playerId) {
        }
        else if (eventArgs.playerIdList) {
            for (let i = 0; i < eventArgs.playerIdList.length; i++) {
                const id = eventArgs.playerIdList[i];
                eventArgs.playerIdList[i] = map.get(id);
            }
        }
    }
    return eventArgs;
}
function convertToRelatedGuilds(params) {
    const list = [];
    if (params.eventArgs) {
        const eventArgs = params.eventArgs;
        if (eventArgs.guildId) {
            list.push({ guildId: eventArgs.guildId, guildName: eventArgs.guildName });
        }
        if (eventArgs.targetGuildId) {
            list.push({ guildId: eventArgs.targetGuildId, guildName: eventArgs.targetGuildName });
        }
    }
    return list;
}
function getIsFirstStatus(eventArgs) {
    if (eventArgs && eventArgs.first) {
        return eventArgs.first;
    }
    else {
        return 0;
    }
}
/** 获取玩家编年史事件列表 */
async function getPlayerAnnalEventList(params) {
    const { roleid } = params;
    const ret = { list: [], count: 0 };
    const pqRet = await models_1.PlayerAnnalEventModel.powerListQuery({
        where: { RoleId: roleid, Status: 0 /* Statues.Normal */ },
        pagination: { page: params.page, pageSize: params.pageSize },
        select: models_1.PlayerAnnalEventCols,
        orderBy: [['CreateTime'], ['desc']]
    });
    ret.count = pqRet.meta.totalCount;
    const rows = pqRet.list;
    const playerIds = _.uniq(_.flatMap(rows, (r) => (0, util_1.csvStrToIntArray)(r.PlayerIds)));
    const transPlayerIdMap = await (0, serverTransfer_1.getLatestTransferRoleIdsMap)(playerIds);
    ret.list = rows.map((r) => {
        const playerIds = (0, util_1.csvStrToIntArray)(r.PlayerIds).map((id) => transPlayerIdMap.get(id));
        const names = (0, util_1.csvStrToArray)(r.PlayerNames);
        const players = _.zipWith(playerIds, names, (roleId, roleName) => {
            return { roleId, roleName };
        });
        let eventArgs = JSON.parse(r.EventArgs);
        eventArgs = formatPlayerIdsInEventArgs(eventArgs, transPlayerIdMap);
        return {
            id: r.ID,
            roleId: r.RoleId,
            serverId: r.ServerId,
            eventType: r.EventType,
            eventTime: r.EventTime,
            eventArgs: eventArgs,
            players: players,
        };
    });
    // 查看列表后关闭红点
    await redDot_1.redDotChecker.disablePlayerAnnalRedDot(roleid);
    return ret;
}
function convertToRelatedPlayers(params) {
    const list = [];
    if (params.eventArgs) {
        const eventArgs = params.eventArgs;
        if (eventArgs.playerId) {
            list.push({ roleId: eventArgs.playerId, roleName: eventArgs.playerName });
        }
        else if (eventArgs.playerIdList) {
            for (let i = 0; i < eventArgs.playerIdList.length; i++) {
                const roleId = eventArgs.playerIdList[i];
                const roleName = eventArgs.playerNameList[i];
                list.push({ roleId, roleName });
            }
        }
    }
    return list;
}
/** 游戏服务器同步编年史事件 (CallByGameServer) */
async function playerAnnalSync(params) {
    const first = getIsFirstStatus(params.eventArgs);
    const week = (0, util_1.getSunWeekStartDayStr)(new Date(params.eventTime));
    const roleId = params.roleId;
    const createProps = {
        UUID: params.uuid,
        RoleId: roleId,
        ServerId: params.serverId,
        EventType: params.eventType,
        EventTime: params.eventTime,
        EventArgs: JSON.stringify(params.eventArgs),
        PlayerIds: "",
        PlayerNames: "",
        GuildIds: "",
        GuildNames: "",
        Status: 0,
        Week: week,
        First: first,
        CreateTime: Date.now(),
    };
    const players = convertToRelatedPlayers(params);
    const guilds = convertToRelatedGuilds(params);
    if (players) {
        createProps.PlayerIds = (0, util_1.arrayToCsv)(players.map((p) => p.roleId));
        createProps.PlayerNames = (0, util_1.arrayToCsv)(players.map((p) => p.roleName));
    }
    if (guilds) {
        createProps.GuildIds = (0, util_1.arrayToCsv)(guilds.map((p) => p.guildId));
        createProps.GuildNames = (0, util_1.arrayToCsv)(guilds.map((p) => p.guildName));
    }
    const updateProps = _.omit(createProps, ["UUID", "CreateTime"]);
    const info = await models_1.PlayerAnnalEventModel.createOrUpdate(createProps, updateProps);
    logger.info({ createProps, updateProps }, "syncPlayerAnnal");
    const ret = { insertId: info.insertId, affectedRows: info.affectedRows };
    // 推送后开启玩家红点
    await redDot_1.redDotChecker.enablePlayerAnnalRedDot(params.roleId);
    return ret;
}
//# sourceMappingURL=operation.js.map
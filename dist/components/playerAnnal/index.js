"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerAnnalComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/player_annal/list",
        paramsSchema: type_1.ReqSchemas.PlayerAnnalList,
        operation: operation_1.getPlayerAnnalEventList,
    },
    {
        method: "post",
        url: "/player_annal/sync",
        paramsSchema: type_1.ReqSchemas.PlayerAnnalSync,
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.playerAnnalSync,
        option: { skipSkey: true },
    },
];
exports.PlayerAnnalComponent = {
    paths: exports.paths,
    prefix: "/player_annal/",
};
//# sourceMappingURL=index.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerAnnalRedDot = void 0;
const constants_1 = require("../../../common/constants");
const redis_1 = require("../../../common/redis");
const util_1 = require("../../../common/util");
class PlayerAnnalRedDot {
    constructor(roleId) {
        this.key = (0, util_1.cacheKeyGen)('player_annal_red_dot', { roleId });
    }
    async has() {
        const ret = await (0, redis_1.getRedis)().existsAsync(this.key);
        return ret === redis_1.IExistResult.Exist;
    }
    async enable() {
        const ret = await (0, redis_1.getRedis)().setAsync(this.key, 1, redis_1.ExpireType.EX, constants_1.ONE_DAY_SECONDS * 365);
        return ret;
    }
    async disable() {
        const ret = await (0, redis_1.getRedis)().delAsync(this.key);
        return ret;
    }
    static create(roleId) {
        return new PlayerAnnalRedDot(roleId);
    }
}
exports.PlayerAnnalRedDot = PlayerAnnalRedDot;
//# sourceMappingURL=redDot.js.map
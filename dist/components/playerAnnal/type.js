"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    PlayerAnnalList: {
        roleid: { type: Number },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    PlayerAnnalSync: {
        uuid: { type: String },
        roleId: { type: Number },
        serverId: { type: Number },
        eventType: { type: Number },
        eventTime: { type: Number },
        eventArgs: {
            type: Object,
            props: {
                playerId: { type: Number, required: false },
                playerIdList: { type: Array, required: false, items: { type: Number } },
                playerName: { type: String, required: false },
                playerNameList: { type: Array, required: false, items: { type: String } },
                guildId: { type: Number, required: false },
                guildName: { type: String, required: false },
                targetGuildId: { type: Number, required: false },
                targetGuildName: { type: String, required: false },
                first: { type: Number, required: false, values: [0, 1] },
                condIndex: { type: Number, required: false },
            },
        },
    },
};
//# sourceMappingURL=type.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWeekRenQiGm = getWeekRenQiGm;
exports.setWeekRenQiGm = setWeekRenQiGm;
const WeekRank_1 = require("../../services/WeekRank");
/** 读取周人气 */
async function getWeekRenQiGm(params) {
    let data = {
        renqi: 0,
    };
    const renqi = await (0, WeekRank_1.getWeekRenqi)(params.roleid, new Date(params.ds));
    data.renqi = renqi;
    return data;
}
/** 设置周人气 */
async function setWeekRenQiGm(params) {
    let data = {
        renqi: 0,
    };
    await (0, WeekRank_1.setWeekRenqi)(params.roleid, params.renqi, new Date(params.ds));
    data.renqi = params.renqi;
    return data;
}
//# sourceMappingURL=operation.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmWeekRenqiComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/gm/week_renqi/get",
        paramsSchema: type_1.ReqSchemas.GetWeekRenqiGm,
        operation: operation_1.getWeekRenQiGm,
    },
    {
        method: "post",
        url: "/gm/week_renqi/set",
        paramsSchema: type_1.ReqSchemas.SetWeekRenQiGm,
        operation: operation_1.setWeekRenQiGm,
    },
];
exports.GmWeekRenqiComponent = {
    paths: exports.paths,
    prefix: "/gm/week_renqi",
};
//# sourceMappingURL=index.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarriagePhotoWallComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/marriage/photo_wall/list",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallList,
        operation: operation_1.marriagePhotoWallList,
    },
    {
        method: "get",
        url: "/marriage/photo_wall/photo/:id/show",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdShow,
        operation: operation_1.marriagePhotoWallPhotoIdShow,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/photo/:id/move",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdMove,
        operation: operation_1.marriagePhotoWallPhotoIdMove,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/handpick_wall/up",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallHandpickWallUp,
        operation: operation_1.marriagePhotoWallHandpickWallUp,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/marriage/photo_wall/handpick_wall/down",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallHandpickWallDown,
        operation: operation_1.marriagePhotoWallHandpickWallDown,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/marriage/photo_wall/photo/:id/del",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdDel,
        operation: operation_1.marriagePhotoWallPhotoIdDel,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/marriage/photo_wall/photo/:id/like",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdLike,
        operation: operation_1.marriagePhotoWallPhotoIdLike,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/photo/:id/cancel_like",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdCancelLike,
        operation: operation_1.marriagePhotoWallPhotoIdCancelLike,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/comment/:id/like",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallCommentIdLike,
        operation: operation_1.marriagePhotoWallCommentIdLike,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/comment/:id/cancel_like",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallCommentIdCancelLike,
        operation: operation_1.marriagePhotoWallCommentIdCancelLike,
    },
    {
        method: "get",
        url: "/marriage/photo_wall/photo/:id/comment/list",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdCommentList,
        operation: operation_1.marriagePhotoWallPhotoIdCommentList,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/photo/:id/comment/add",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoIdCommentAdd,
        operation: operation_1.marriagePhotoWallPhotoIdCommentAdd,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/:id/photo/add",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallIdPhotoAdd,
        operation: operation_1.marriagePhotoWallIdPhotoAdd,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/photo/:photo_id/comment/del",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallPhotoPhotoIdCommentDel,
        operation: operation_1.marriagePhotoWallPhotoIdCommentDel,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/marriage/photo_wall/:id/show",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallIdShow,
        operation: operation_1.marriagePhotoWallIdShow,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/:id/update",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallIdUpdate,
        operation: operation_1.marriagePhotoWallIdUpdate,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/marriage/photo_wall/add",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallAdd,
        operation: operation_1.marriagePhotoWallAdd,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/marriage/photo_wall/notifications/list",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallNotificationsList,
        operation: operation_1.marriagePhotoWallNotificationsList,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/notifications/read",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallNotificationsRead,
        operation: operation_1.marriagePhotoWallNotificationsRead,
    },
    {
        method: "post",
        url: "/marriage/photo_wall/notifications/read_all",
        paramsSchema: type_1.ReqSchemas.MarriagePhotoWallNotificationsReadAll,
        operation: operation_1.marriagePhotoWallNotificationsReadAll,
    },
];
exports.MarriagePhotoWallComponent = {
    paths: exports.paths,
    prefix: "/marriage/photo_wall/",
};
//# sourceMappingURL=index.js.map
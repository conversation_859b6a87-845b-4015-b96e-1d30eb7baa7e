"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.marriagePhotoWallList = marriagePhotoWallList;
exports.marriagePhotoWallPhotoIdDel = marriagePhotoWallPhotoIdDel;
exports.marriagePhotoWallPhotoIdShow = marriagePhotoWallPhotoIdShow;
exports.marriagePhotoWallPhotoIdMove = marriagePhotoWallPhotoIdMove;
exports.marriagePhotoWallHandpickWallUp = marriagePhotoWallHandpickWallUp;
exports.marriagePhotoWallHandpickWallDown = marriagePhotoWallHandpickWallDown;
exports.marriagePhotoWallPhotoIdLike = marriagePhotoWallPhotoIdLike;
exports.marriagePhotoWallPhotoIdCancelLike = marriagePhotoWallPhotoIdCancelLike;
exports.marriagePhotoWallCommentIdLike = marriagePhotoWallCommentIdLike;
exports.marriagePhotoWallCommentIdCancelLike = marriagePhotoWallCommentIdCancelLike;
exports.marriagePhotoWallPhotoIdCommentList = marriagePhotoWallPhotoIdCommentList;
exports.marriagePhotoWallPhotoIdCommentAdd = marriagePhotoWallPhotoIdCommentAdd;
exports.marriagePhotoWallPhotoIdCommentDel = marriagePhotoWallPhotoIdCommentDel;
exports.marriagePhotoWallIdPhotoAdd = marriagePhotoWallIdPhotoAdd;
exports.marriagePhotoWallIdShow = marriagePhotoWallIdShow;
exports.marriagePhotoWallIdUpdate = marriagePhotoWallIdUpdate;
exports.marriagePhotoWallAdd = marriagePhotoWallAdd;
exports.marriagePhotoWallNotificationsList = marriagePhotoWallNotificationsList;
exports.marriagePhotoWallNotificationsRead = marriagePhotoWallNotificationsRead;
exports.marriagePhotoWallNotificationsReadAll = marriagePhotoWallNotificationsReadAll;
const _ = require("lodash");
const util_1 = require("util");
const cacheUtil_1 = require("../../common/cacheUtil");
const constants_1 = require("../../common/constants");
const util_2 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
const UserPermission_1 = require("../../models/UserPermission");
const redDot_1 = require("../../services/redDot");
const roleInfo_1 = require("../../services/roleInfo");
const constant_1 = require("./constant");
const service_1 = require("./service");
/** 获取帮会当前图片数量 */
async function getPhotoCntByMarriageId(marriageId) {
    const rows = await models_1.MarriagePhotoWallModel.find({ status: 0 /* Statues.Normal */, marriageId, type: 0 /* EWallType.Normal */ });
    const wallIds = rows.map(r => r.id);
    const photoCnt = await models_1.MarriagePhotoWallPhotoModel.count({ wallId: wallIds, status: 0 /* Statues.Normal */ });
    return photoCnt;
}
/** 查看帮会所在照片墙列表 */
async function marriagePhotoWallList(params) {
    const { marriageId } = params;
    const rows = await models_1.MarriagePhotoWallModel.find({ status: 0 /* Statues.Normal */, marriageId });
    const list = rows.map((r) => {
        return {
            id: r.id,
            type: r.type,
            createTime: r.createTime,
            name: r.name,
            templateId: r.templateId,
            frameId: r.frameId,
        };
    });
    const wallIds = rows.filter(r => r.type === 0 /* EWallType.Normal */).map(r => r.id);
    const photoCnt = await models_1.MarriagePhotoWallPhotoModel.count({ wallId: wallIds, status: 0 /* Statues.Normal */ });
    const data = { list, photoCnt };
    return data;
}
/** 删除照片墙的这张图片 (CallByGameServer) */
async function marriagePhotoWallPhotoIdDel(params) {
    const { roleid, id } = params;
    const photo = await (0, service_1.findPhoto)(id);
    if (!photo) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoNotExist);
    }
    const wall = await (0, service_1.findWall)(photo.wallId);
    const hasPermission = roleid === photo.roleId || params.isLeader || wall.marriageId === params.marriageId;
    if (!hasPermission) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoDelPermissionReject);
    }
    const ret = await models_1.MarriagePhotoWallPhotoModel.softDeleteById(photo.id);
    // 删除图片的时候， 同步处理下精选的关系，把上架关系同步删除
    await models_1.MarriagePhotoWallHandpickModel.deleteByCondition({ photoId: photo.id });
    const isOk = ret.affectedRows > 0;
    if (isOk) {
        await (0, service_1.addNotificationByAction)(5 /* ENotificationsType.PhotoBeDel */, {
            roleId: roleid,
            targetId: photo.roleId,
            photoId: photo.id,
            wallId: photo.wallId,
            text: "",
            relateId: photo.id,
        });
    }
    const data = { isOk };
    return data;
}
/** 查看照片墙中具体图片 */
async function marriagePhotoWallPhotoIdShow(params) {
    const { roleid, id } = params;
    const photo = await (0, service_1.findPhoto)(id);
    const wall = await (0, service_1.findWall)(photo.wallId);
    const atRoleIds = (0, service_1.formatAtRoleIds)(photo);
    const hotState = (0, service_1.formatHotState)(photo);
    const isLiked = await (0, service_1.isLikePhoto)(roleid, id);
    const isPicked = await (0, service_1.isPickedPhoto)(id);
    const roleName = await (0, roleInfo_1.getRoleName)(photo.roleId);
    const urlShow = (0, service_1.getPhotoUrl)(photo.url, photo.auditStatus, photo.roleId === roleid, true);
    const auditStatus = (0, service_1.getAuditStatus)(photo.auditStatus);
    const data = Object.assign({}, (0, util_2.pickBy)(photo, ["id", "roleId", "auditStatus", "createTime", "text", "slot", "wallId"]), {
        auditStatus,
        photoId: photo.id,
        url: urlShow,
        marriageId: wall.marriageId,
        roleName: roleName,
        isPicked,
        atRoleIds: atRoleIds,
        isLiked,
        likeCount: hotState.like,
        commentCount: hotState.comment,
    });
    return data;
}
/** 移动照片墙中图片的槽位 */
async function marriagePhotoWallPhotoIdMove(params) {
    const { id, slot } = params;
    const r = await (0, service_1.findPhoto)(id);
    if (r.slot === slot) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.WallMovePhotoSlotSame);
    }
    const targetSlotPhoto = await models_1.MarriagePhotoWallPhotoModel.findOne({ wallId: r.wallId, slot }, ["id"]);
    if (targetSlotPhoto) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.WallMovePhotoSlotNotEmpty);
    }
    const upRet = await models_1.MarriagePhotoWallPhotoModel.updateById(r.id, { slot });
    const isOk = upRet.affectedRows > 0;
    const data = { isOk };
    return data;
}
/** 帮会当家从精选墙上架图片 (CallByGameServer) */
async function marriagePhotoWallHandpickWallUp(params) {
    const { roleid, wallId, photoId } = params;
    const wall = await (0, service_1.findHandPickWall)(wallId);
    const photo = await (0, service_1.findPhoto)(photoId);
    const handpickSlot = await models_1.MarriagePhotoWallHandpickModel.findOne({ wallId: wall.id, slot: params.slot });
    if (handpickSlot) {
        const photo = await models_1.MarriagePhotoWallModel.findNormalById(handpickSlot.photoId);
        if (photo) {
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.HandPickWallSlotUsed);
        }
        // 照片不存在了，删除精选的关系
        await models_1.MarriagePhotoWallHandpickModel.delete({
            id: handpickSlot.id
        });
    }
    const insertProps = {
        wallId: wall.id,
        photoId: photo.id,
        slot: params.slot,
    };
    const id = await models_1.MarriagePhotoWallHandpickModel.insert(insertProps);
    await (0, service_1.addNotificationByAction)(6 /* ENotificationsType.PhotoHandPick */, {
        roleId: roleid,
        targetId: photo.roleId,
        photoId: photo.id,
        wallId: wallId,
        text: "",
        relateId: wallId,
    });
    const data = { id };
    return data;
}
/** 帮会当家从精选墙下架图片(CallByGameServer) */
async function marriagePhotoWallHandpickWallDown(params) {
    const upRet = await models_1.MarriagePhotoWallHandpickModel.deleteByCondition({ wallId: params.wallId, slot: params.slot });
    const isOk = upRet.affectedRows > 0;
    const data = { isOk };
    return data;
}
/** 点赞照片墙中具体图片 */
async function marriagePhotoWallPhotoIdLike(params) {
    const roleId = params.roleid;
    const photoId = params.id;
    const photo = await (0, service_1.findPhoto)(photoId);
    const likeRecord = await models_1.MarriagePhotoWallPhotoLikeModel.findOne({ roleId, photoId });
    let newId = 0;
    if (likeRecord) {
        if (likeRecord.status === -1 /* Statues.Deleted */) {
            await models_1.MarriagePhotoWallPhotoLikeModel.updateById(likeRecord.id, { status: 0 /* Statues.Normal */ });
            newId = likeRecord.id;
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoAlreadyLiked);
        }
    }
    else {
        const insertProps = {
            roleId,
            targetId: photo.roleId,
            photoId,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        };
        newId = await models_1.MarriagePhotoWallPhotoLikeModel.insert(insertProps);
    }
    const data = { id: newId };
    await (0, service_1.updatePhotoHotState)(photo, 0 /* EPhotoAction.Like */);
    await (0, service_1.addNotificationByAction)(1 /* ENotificationsType.PhotoBeLiked */, {
        roleId: roleId,
        targetId: photo.roleId,
        photoId: photo.id,
        wallId: photo.wallId,
        text: "",
        relateId: newId,
    });
    return data;
}
/** 取消点赞照片墙中具体图片 */
async function marriagePhotoWallPhotoIdCancelLike(params) {
    const roleId = params.roleid;
    const photoId = params.id;
    const photo = await (0, service_1.findPhoto)(photoId);
    const likeRecord = await models_1.MarriagePhotoWallPhotoLikeModel.findOne({ roleId, photoId, status: 0 /* Statues.Normal */ });
    let newId = 0;
    if (likeRecord) {
        await models_1.MarriagePhotoWallPhotoLikeModel.updateById(likeRecord.id, { status: -1 /* Statues.Deleted */ });
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoNotLiked);
    }
    const data = { id: newId };
    await (0, service_1.updatePhotoHotState)(photo, 1 /* EPhotoAction.CancelLike */);
    return data;
}
/** 点赞照片的评论 */
async function marriagePhotoWallCommentIdLike(params) {
    const roleId = params.roleid;
    const commentId = params.id;
    const comment = await (0, service_1.findComment)(commentId);
    const likeRecord = await models_1.MarriagePhotoWallCommentLikeModel.findOne({ roleId, commentId });
    let newId = 0;
    if (likeRecord) {
        if (likeRecord.status === -1 /* Statues.Deleted */) {
            await models_1.MarriagePhotoWallCommentLikeModel.updateById(likeRecord.id, { status: 0 /* Statues.Normal */ });
            newId = likeRecord.id;
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.CommentAlreadyLiked);
        }
    }
    else {
        const insertProps = {
            roleId,
            targetId: comment.roleId,
            commentId: commentId,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        };
        newId = await models_1.MarriagePhotoWallCommentLikeModel.insert(insertProps);
    }
    await (0, service_1.updateCommentLikeCount)(comment, 0 /* ECommentAction.Like */);
    const data = { id: newId };
    return data;
}
/** 取消点赞照片的评论 */
async function marriagePhotoWallCommentIdCancelLike(params) {
    const roleId = params.roleid;
    const commentId = params.id;
    const comment = await (0, service_1.findComment)(commentId);
    const likeRecord = await models_1.MarriagePhotoWallCommentLikeModel.findOne({ roleId, commentId, status: 0 /* Statues.Normal */ });
    let newId = 0;
    if (likeRecord) {
        await models_1.MarriagePhotoWallCommentLikeModel.updateById(likeRecord.id, { status: -1 /* Statues.Deleted */ });
        newId = likeRecord.id;
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoNotLiked);
    }
    await (0, service_1.updateCommentLikeCount)(comment, 1 /* ECommentAction.CancelLike */);
    const data = { id: newId };
    return data;
}
/** 查看照片墙图片的评论列表 */
async function marriagePhotoWallPhotoIdCommentList(params) {
    const result = await models_1.MarriagePhotoWallCommentModel.powerListQuery({
        where: { targetId: params.id, status: 0 /* Statues.Normal */ },
        select: models_1.MarriagePhotoWallCommentCols,
        pagination: {
            page: params.page,
            pageSize: params.pageSize,
        },
    });
    const listRows = result.list;
    const fetchRoleIds = _.compact(_.flatMap(listRows, (r) => [r.roleId, r.replyId]));
    const fetchRoleName = await (0, service_1.getRoleNameFetcher)(fetchRoleIds);
    const likePhotoIdSet = await (0, service_1.getLikedPhotoSet)(params.roleid, fetchRoleIds);
    const list = listRows.map((r) => {
        const baseProps = (0, util_2.pickBy)(r, ["id", "roleId", "replyId", "likeCount", "createTime", "text"]);
        return Object.assign({}, baseProps, {
            photoId: r.targetId,
            roleName: fetchRoleName(r.roleId),
            replyName: fetchRoleName(r.targetId),
            isLiked: likePhotoIdSet.has(r.id),
        });
    });
    const data = { list, meta: result.meta };
    return data;
}
/** 添加照片墙图片的评论 */
async function marriagePhotoWallPhotoIdCommentAdd(params) {
    const { roleid, id, text } = params;
    const photo = await (0, service_1.findPhoto)(id);
    await (0, UserPermission_1.checkPermission)(roleid, UserPermission_1.Permission.GPWComment, '您已被管理员禁止评论帮会照片墙');
    const commentType = params.replyId > 0 ? 1 /* ECommentType.Reply */ : 0 /* ECommentType.Comment */;
    const insertProps = {
        roleId: params.roleid,
        replyId: params.replyId || 0,
        targetId: photo.id,
        likeCount: 0,
        topTime: 0,
        text,
        type: commentType,
        status: 0 /* Statues.Normal */,
        createTime: Date.now(),
    };
    const newId = await models_1.MarriagePhotoWallCommentModel.insert(insertProps);
    await (0, service_1.updatePhotoHotState)(photo, 2 /* EPhotoAction.Comment */);
    // 图片被评论的通知
    async function addPhotoBeCommentNotification() {
        await (0, service_1.addNotificationByAction)(2 /* ENotificationsType.PhotoBeComment */, {
            roleId: params.roleid,
            targetId: photo.roleId,
            photoId: photo.id,
            wallId: photo.wallId,
            text: text,
            relateId: newId,
        });
    }
    if (commentType === 0 /* ECommentType.Comment */) {
        await addPhotoBeCommentNotification();
    }
    else if (commentType === 1 /* ECommentType.Reply */) {
        // 图片评论被回复的通知
        await (0, service_1.addNotificationByAction)(4 /* ENotificationsType.PhotoCommentBeReply */, {
            roleId: params.roleid,
            targetId: params.replyId,
            photoId: photo.id,
            wallId: photo.wallId,
            text: text,
            relateId: newId,
        });
        // 如果回复的玩家id不是图片的拥有者id， 给图片拥有者也发一条通知
        if (params.replyId !== photo.roleId) {
            await addPhotoBeCommentNotification();
        }
    }
    const data = { id: newId };
    return data;
}
/** 删除照片墙图片的评论 */
async function marriagePhotoWallPhotoIdCommentDel(params) {
    const c = await (0, service_1.findComment)(params.id);
    if (!c) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.CommentNotExist);
    }
    const photo = await (0, service_1.findPhoto)(c.targetId);
    const wall = await (0, service_1.findWall)(photo.wallId);
    const hasPermission = params.roleid === c.roleId || params.isLeader || params.marriageId === wall.marriageId;
    if (!hasPermission) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.CommentDelPermissionReject);
    }
    const upRet = await models_1.MarriagePhotoWallCommentModel.softDeleteById(c.id);
    const isOk = upRet.affectedRows > 0;
    if (isOk) {
        await (0, service_1.updatePhotoHotState)(photo, 3 /* EPhotoAction.DelComment */);
    }
    const data = { isOk };
    return data;
}
/** 上传照片墙里的图片 */
async function marriagePhotoWallIdPhotoAdd(params) {
    const { roleid } = params;
    const wallId = params.id;
    const curTargetSlot = await models_1.MarriagePhotoWallPhotoModel.findOne({ wallId: wallId, slot: params.slot, status: 0 /* Statues.Normal */ });
    if (curTargetSlot) {
        if (params.overwrite) {
            await models_1.MarriagePhotoWallPhotoModel.deleteByCondition({ id: curTargetSlot.id });
            await models_1.MarriagePhotoWallHandpickModel.deleteByCondition({ photoId: curTargetSlot.id });
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoAddSlotNotEmpty);
        }
    }
    const atRoleIds = _.take(params.atRoleIds || [], constant_1.MaxAtRoleIdSize);
    const insertProps = {
        roleId: params.roleid,
        wallId: wallId,
        url: params.url,
        auditStatus: constants_1.EAuditStatus.Init,
        text: params.text || "",
        createTime: Date.now(),
        atRoleIds: (0, util_2.arrayToCsv)(atRoleIds),
        slot: params.slot,
        hot: 0,
        hotState: JSON.stringify(service_1.INIT_HOT_STATE),
        status: 0 /* Statues.Normal */,
    };
    const insertId = await models_1.MarriagePhotoWallPhotoModel.insert(insertProps);
    const data = { id: insertId };
    // at的玩家一次性插入通知
    if (_.isArray(atRoleIds) && atRoleIds.length > 0) {
        const atNotificationList = atRoleIds.map((targetId) => {
            return (0, service_1.buildNotificationByAction)(3 /* ENotificationsType.PhotoBeAt */, {
                roleId: params.roleid,
                targetId,
                photoId: insertId,
                wallId: wallId,
                text: "",
                relateId: insertId,
            });
        });
        await (0, service_1.addNotifications)(atNotificationList);
    }
    if (params.url && insertId > 0) {
        // photo wall exract ip
        (0, service_1.auditMarriagePhotoWallPhoto)(roleid, params.url, insertId, "");
    }
    return data;
}
async function getPhotoWallPhotos(wall) {
    const wallId = wall.id;
    let photosRows = [];
    if (wall.type === 0 /* EWallType.Normal */) {
        const photosRecords = await models_1.MarriagePhotoWallPhotoModel.find({ status: 0 /* Statues.Normal */, wallId: wallId }, { limit: constant_1.MaxPhotosSizePerWall });
        photosRows = photosRecords.map(r => Object.assign({}, r, { photoId: r.id }));
    }
    else {
        const photoHandPickRows = await models_1.MarriagePhotoWallHandpickModel.find({ wallId }, { limit: constant_1.MaxPhotosSizePerWall });
        const photoIds = photoHandPickRows.map((r) => r.photoId);
        const photosRowsInner = await models_1.MarriagePhotoWallPhotoModel.find({ id: photoIds, status: 0 /* Statues.Normal */ });
        const photoMap = (0, util_2.keyToRecordMap)(photosRowsInner, 'id');
        const simPhotoRows = photoHandPickRows.map(r => {
            const photo = photoMap.get(r.photoId);
            if (photo) {
                return {
                    id: r.id,
                    photoId: r.photoId,
                    slot: r.slot,
                    roleId: photo.roleId,
                    wallId: wallId,
                    url: photo.url,
                    text: photo.text,
                    createTime: photo.createTime,
                    hot: photo.hot,
                    hotState: photo.hotState,
                    status: photo.status,
                    atRoleIds: photo.atRoleIds,
                    auditStatus: photo.auditStatus
                };
            }
            else {
                return null;
            }
        });
        photosRows = simPhotoRows.filter(r => !!r);
    }
    return photosRows;
}
/** 查看照片墙 */
async function marriagePhotoWallIdShow(params) {
    const roleId = params.roleid;
    const wallId = params.id;
    let photos = [];
    let gpw = {
        name: "",
        templateId: 0,
        frameId: 0,
        type: 0,
    };
    const wall = await (0, service_1.findWall)(wallId);
    gpw = { name: wall.name, templateId: wall.templateId, frameId: wall.frameId, type: wall.type };
    const photosRows = await getPhotoWallPhotos(wall);
    const queryRoleIds = photosRows.map((r) => r.roleId);
    const roleNamesMap = await (0, roleInfo_1.getRoleNameMap)(queryRoleIds);
    const getName = (roleId) => (roleNamesMap.has(roleId) ? roleNamesMap.get(roleId) : "");
    const photoIds = photosRows.map((r) => r.id);
    const likedSet = await (0, service_1.getLikedPhotoSet)(roleId, photoIds);
    const pickedSet = await (0, service_1.getPickedPhotoSet)(photoIds);
    photos = photosRows.map(function (r) {
        const roleName = getName(r.roleId);
        const isLiked = likedSet.has(r.id);
        const isPicked = pickedSet.has(r.id);
        const atRoleIds = (0, service_1.formatAtRoleIds)(r);
        const hotState = (0, service_1.formatHotState)(r);
        const urlShow = (0, service_1.getPhotoUrl)(r.url, r.auditStatus, r.roleId === roleId);
        const auditStatus = (0, service_1.getAuditStatus)(r.auditStatus);
        const item = Object.assign({}, (0, util_2.pickBy)(r, ["id", "photoId", "roleId", "auditStatus", "createTime", "text", "slot", "wallId"]), {
            auditStatus,
            url: urlShow,
            marriageId: wall.marriageId,
            roleName: roleName,
            atRoleIds: atRoleIds,
            isPicked,
            isLiked,
            likeCount: hotState.like,
            commentCount: hotState.comment,
        });
        return item;
    });
    const data = {
        id: wallId,
        photos,
        name: gpw.name,
        templateId: gpw.templateId,
        frameId: gpw.frameId,
        type: gpw.type,
    };
    return data;
}
/** 更新照片墙信息 */
async function marriagePhotoWallIdUpdate(params) {
    const wall = await (0, service_1.findWall)(params.id);
    if (wall.type === 1 /* EWallType.HandPick */) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.HandPickWallModifyDisallow);
    }
    const updateProps = {};
    if (params.name) {
        updateProps.name = params.name;
    }
    if (params.templateId) {
        updateProps.templateId = params.templateId;
    }
    if (params.frameId) {
        updateProps.frameId = params.frameId;
    }
    const ret = await models_1.MarriagePhotoWallModel.updateById(params.id, updateProps);
    const data = { isOk: ret.affectedRows > 0 };
    return data;
}
/** 开启新的照片墙 */
async function marriagePhotoWallAdd(params) {
    const { marriageId, marriageLevel } = params;
    const key = (0, util_2.cacheKeyGen)("marriage_photo_wall_add", { marriageId: params.marriageId, idx: params.idx || 0 });
    const lock = new cacheUtil_1.OperationInterval(key);
    const locked = await lock.locked(5000);
    if (locked) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.AddWallTooOften);
    }
    if (params.type == 1 /* EWallType.HandPick */) {
        if (params.marriageLevel < constant_1.OpenHandPickLimit.MinMarriageLevel) {
            await lock.unlock();
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.HandPickWallEnableGuildLevelNotEnough);
        }
        const photoCnt = await getPhotoCntByMarriageId(params.marriageId);
        if (photoCnt < constant_1.OpenHandPickLimit.MinPhotoCnt) {
            await lock.unlock();
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.HandPickWallEnablePhotoCntNotEnough);
        }
        const curHandPickWallCnt = await models_1.MarriagePhotoWallModel.count({ marriageId, type: 1 /* EWallType.HandPick */ });
        if (curHandPickWallCnt >= constant_1.MaxHandPickWallSize) {
            await lock.unlock();
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.HandPickWallSizeReachLimit);
        }
    }
    else {
        // 普通墙开启限制
        const curNormalWallCnt = await models_1.MarriagePhotoWallModel.count({ marriageId, type: 0 /* EWallType.Normal */ });
        const normalWallMaxCnt = (0, service_1.getNormalWallUpperBound)(marriageLevel);
        if (curNormalWallCnt >= normalWallMaxCnt) {
            await lock.unlock();
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.NormalWallSizeReachLimit);
        }
    }
    if (!(0, util_1.isNullOrUndefined)(params.idx)) {
        const wall = await models_1.MarriagePhotoWallModel.findOne({
            marriageId,
            idx: params.idx,
        });
        if (wall) {
            await lock.unlock();
            return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.IdxAlreadyExist);
        }
    }
    const insertProps = {
        marriageId: params.marriageId,
        type: params.type || 0 /* EWallType.Normal */,
        founder: params.roleid || 0,
        templateId: params.templateId,
        frameId: params.frameId || 0,
        name: params.name || "",
        createTime: Date.now(),
        status: 0 /* Statues.Normal */,
        idx: params.idx || 0,
    };
    const id = await models_1.MarriagePhotoWallModel.insert(insertProps);
    await lock.unlock();
    const data = { id };
    return data;
}
/** 通知列表 */
async function marriagePhotoWallNotificationsList(params) {
    const { roleid } = params;
    let statusList = [0 /* Statues.Normal */, 1 /* Statues.Read */];
    if (params.status === 0 /* Statues.Normal */) {
        statusList = [0 /* Statues.Normal */];
    }
    else if (params.status === 1 /* Statues.Read */) {
        statusList = [1 /* Statues.Read */];
    }
    const result = await models_1.MarriagePhotoWallNotificationModel.powerListQuery({
        where: { targetId: roleid, status: statusList },
        select: models_1.MarriagePhotoWallNotificationCols,
        pagination: { page: params.page, pageSize: params.pageSize },
        orderBy: [["id"], ["desc"]],
    });
    const listRows = result.list;
    const queryRoleIds = listRows.map((r) => r.roleId);
    const photoIds = _.compact(listRows.map((r) => r.photoId));
    const roleInfoFetcher = await (0, service_1.getRoleInfoFetcher)(queryRoleIds, roleid);
    const photoUrlFetcher = await (0, service_1.getPhotoUrlFetcher)(photoIds);
    const list = listRows.map((r) => {
        const p = roleInfoFetcher(r.roleId) || {};
        return Object.assign({}, (0, util_2.pickBy)(r, ["id", "roleId", "targetId", "type", "text", "photoId", "wallId", "status", "createTime"]), {
            roleName: p.RoleName,
            jobId: p.JobId,
            gender: p.Gender,
            server: p.ServerId,
            level: p.Level,
            frameId: p.frameId,
            photoUrl: photoUrlFetcher(r.photoId),
        });
    });
    // 策划要求打开列表后红点自动消失
    await (0, redDot_1.marriagePhotoWallRedDotDisable)(roleid);
    const data = { list, meta: result.meta };
    return data;
}
/** 单个通知设为已读 */
async function marriagePhotoWallNotificationsRead(params) {
    const ret = await models_1.MarriagePhotoWallNotificationModel.updateByCondition({ targetId: params.roleid, id: params.notificationId, status: 0 /* Statues.Normal */ }, { status: 1 /* Statues.Read */ });
    const isOk = ret.affectedRows > 0;
    const data = { isOk };
    return data;
}
/** 通知列表全部设为已读 */
async function marriagePhotoWallNotificationsReadAll(params) {
    const ret = await models_1.MarriagePhotoWallNotificationModel.updateByCondition({ targetId: params.roleid, status: 0 /* Statues.Normal */ }, { status: 1 /* Statues.Read */ });
    const isOk = ret.affectedRows >= 0;
    const data = { isOk };
    await (0, redDot_1.marriagePhotoWallRedDotDisable)(params.roleid);
    return data;
}
//# sourceMappingURL=operation.js.map
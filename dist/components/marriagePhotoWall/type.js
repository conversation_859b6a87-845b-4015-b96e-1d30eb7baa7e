"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    MarriagePhotoWallList: {
        roleid: { type: Number },
        marriageId: { type: Number },
    },
    MarriagePhotoWallPhotoIdShow: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallPhotoIdMove: {
        roleid: { type: Number },
        id: { type: Number },
        slot: { type: Number },
    },
    MarriagePhotoWallHandpickWallUp: {
        roleid: { type: Number },
        photoId: { type: Number },
        wallId: { type: Number },
        slot: { type: Number },
    },
    MarriagePhotoWallHandpickWallDown: {
        roleid: { type: Number },
        wallId: { type: Number },
        slot: { type: Number },
    },
    MarriagePhotoWallPhotoIdDel: {
        roleid: { type: Number },
        marriageId: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallPhotoIdLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallPhotoIdCancelLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallCommentIdLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallCommentIdCancelLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallPhotoIdCommentList: {
        roleid: { type: Number },
        id: { type: Number },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    MarriagePhotoWallPhotoIdCommentAdd: {
        roleid: { type: Number },
        id: { type: Number },
        text: { type: String },
        replyId: { type: Number, required: false },
    },
    MarriagePhotoWallPhotoPhotoIdCommentDel: {
        roleid: { type: Number },
        marriageId: { type: Number },
        photo_id: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallIdPhotoAdd: {
        roleid: { type: Number },
        id: { type: Number },
        overwrite: { type: Boolean, required: false, default: true },
        text: { type: String, required: false },
        url: { type: String },
        slot: { type: Number },
        atRoleIds: { type: Array, required: false, items: { type: Number } },
    },
    MarriagePhotoWallIdShow: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MarriagePhotoWallIdUpdate: {
        roleid: { type: Number, required: false, default: 0 },
        id: { type: Number },
        name: { type: String, required: false },
        templateId: { type: Number, required: false },
        frameId: { type: Number, required: false },
        type: { type: Number, required: false, values: [0, 1] },
        idx: { type: Number, required: false },
    },
    MarriagePhotoWallAdd: {
        roleid: { type: Number, required: false, default: 0 },
        marriageId: { type: Number },
        marriageLevel: { type: Number },
        name: { type: String, required: false },
        templateId: { type: Number, required: false },
        frameId: { type: Number, required: false },
        type: { type: Number, required: false, values: [0, 1] },
        idx: { type: Number, required: false },
    },
    MarriagePhotoWallNotificationsList: {
        roleid: { type: Number },
        status: { type: Number, required: false, values: [0, 1] },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    MarriagePhotoWallNotificationsRead: {
        roleid: { type: Number },
        notificationId: { type: Number },
    },
    MarriagePhotoWallNotificationsReadAll: {
        roleid: { type: Number },
    },
};
//# sourceMappingURL=type.js.map
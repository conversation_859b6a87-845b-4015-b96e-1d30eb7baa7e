"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.INIT_HOT_STATE = void 0;
exports.formatAtRoleIds = formatAtRoleIds;
exports.formatHotState = formatHotState;
exports.isLikePhoto = isLikePhoto;
exports.isPickedPhoto = isPickedPhoto;
exports.findPhoto = findPhoto;
exports.findComment = findComment;
exports.findWall = findWall;
exports.findHandPickWall = findHandPickWall;
exports.getLikedPhotoSet = getLikedPhotoSet;
exports.getPickedPhotoSet = getPickedPhotoSet;
exports.getRealWallId = getRealWallId;
exports.getRoleNameFetcher = getRoleNameFetcher;
exports.getPhotoUrlMap = getPhotoUrlMap;
exports.getPhotoUrlFetcher = getPhotoUrlFetcher;
exports.getNewHotState = getNewHotState;
exports.getHotFromState = getHotFromState;
exports.updatePhotoHotState = updatePhotoHotState;
exports.updateCommentLikeCount = updateCommentLikeCount;
exports.addNotificationByAction = addNotificationByAction;
exports.buildNotificationByAction = buildNotificationByAction;
exports.addNotifications = addNotifications;
exports.getMarriagePhotoWallNotificationNumInner = getMarriagePhotoWallNotificationNumInner;
exports.getRoleInfoFetcher = getRoleInfoFetcher;
exports.auditMarriagePhotoWallPhoto = auditMarriagePhotoWallPhoto;
exports.getPhotoUrl = getPhotoUrl;
exports.getAuditStatus = getAuditStatus;
exports.getNormalWallUpperBound = getNormalWallUpperBound;
exports.fixPhotoWallDuplicate = fixPhotoWallDuplicate;
exports.findPhotoWallDuplicate = findPhotoWallDuplicate;
const _ = require("lodash");
const constants_1 = require("../../common/constants");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const roleInfo_1 = require("../../services/roleInfo");
const constant_1 = require("./constant");
const logger = (0, logger_1.clazzLogger)("MarriagePhotoWall/service");
const ProfileService = require("../../services/profile");
const imageAudit_1 = require("../../services/imageAudit");
const sendImageAudit_1 = require("../../services/sendImageAudit");
const config_1 = require("../../common/config");
const redDot_1 = require("../../services/redDot");
function formatAtRoleIds(r) {
    const roleIds = r.atRoleIds || "";
    return (0, util_1.csvStrToIntArray)(roleIds);
}
function formatHotState(r) {
    const hotStatObj = (0, util_1.getJsonInfo)(r.hotState, {});
    if (hotStatObj) {
        return { like: hotStatObj.like || 0, comment: hotStatObj.comment || 0 };
    }
    else {
        return { like: 0, comment: 0 };
    }
}
exports.INIT_HOT_STATE = { like: 0, comment: 0 };
async function isLikePhoto(roleId, photoId) {
    const r = await models_1.MarriagePhotoWallPhotoLikeModel.findOne({ roleId, photoId, status: 0 /* Statues.Normal */ }, ["id"]);
    return !!(r && r.id);
}
async function isPickedPhoto(photoId) {
    const r = await models_1.MarriagePhotoWallHandpickModel.findOne({ photoId }, ["id"]);
    return !!(r && r.id);
}
async function findPhoto(id) {
    const r = await models_1.MarriagePhotoWallPhotoModel.findOne({ id, status: 0 /* Statues.Normal */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.PhotoNotExist);
    }
    return r;
}
async function findComment(id) {
    const r = await models_1.MarriagePhotoWallCommentModel.findOne({ id, status: 0 /* Statues.Normal */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.CommentNotExist);
    }
    return r;
}
async function findWall(id) {
    const r = await models_1.MarriagePhotoWallModel.findOne({ id, status: 0 /* Statues.Normal */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.WallNotExist);
    }
    return r;
}
async function findHandPickWall(id) {
    const r = await models_1.MarriagePhotoWallModel.findOne({ id, status: 0 /* Statues.Normal */, type: 1 /* EWallType.HandPick */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MarriagePhotoWallErrors.HandPickWallNotExist);
    }
    return r;
}
async function getLikedPhotoSet(roleId, photoIds) {
    const rows = await models_1.MarriagePhotoWallPhotoLikeModel.find({ status: 0 /* Statues.Normal */, roleId, photoId: photoIds }, { cols: ["id"] });
    const likeIds = rows.map((r) => r.id);
    return new Set(likeIds);
}
async function getPickedPhotoSet(photoIds) {
    const rows = await models_1.MarriagePhotoWallHandpickModel.find({ photoId: photoIds }, { cols: ["photoId"] });
    const pickPhotoIds = rows.map((r) => r.photoId);
    return new Set(pickPhotoIds);
}
async function getRealWallId(wallVirtualId, marriageId) {
    if (wallVirtualId === constant_1.HandPickWallId) {
        const r = await models_1.MarriagePhotoWallModel.findOne({ marriageId, type: 1 /* EWallType.HandPick */ }, ["id"]);
        if (r && r.id) {
            return 0;
        }
        else {
            return 0;
        }
    }
    else {
        return _.toInteger(wallVirtualId);
    }
}
async function getRoleNameFetcher(roleIds) {
    const roleNamesMap = await (0, roleInfo_1.getRoleNameMap)(roleIds);
    const getName = (roleId) => (roleNamesMap.has(roleId) ? roleNamesMap.get(roleId) : "");
    return getName;
}
async function getPhotoUrlMap(photoIds) {
    const map = new Map();
    const rows = await models_1.MarriagePhotoWallPhotoModel.find({ id: photoIds }, { cols: ["id", "url"] });
    for (let r of rows) {
        map.set(r.id, r.url);
    }
    return map;
}
async function getPhotoUrlFetcher(photoIds) {
    const photoUrlMap = await getPhotoUrlMap(photoIds);
    const getUrl = (id) => (photoUrlMap.has(id) ? photoUrlMap.get(id) : "");
    return getUrl;
}
function getNewHotState(hotState, action) {
    const newState = _.clone(hotState) || { like: 0, comment: 0 };
    if (action === 0 /* EPhotoAction.Like */) {
        newState.like = hotState.like + 1;
    }
    else if (action === 1 /* EPhotoAction.CancelLike */) {
        newState.like = Math.max(0, hotState.like - 1);
    }
    else if (action === 2 /* EPhotoAction.Comment */) {
        newState.comment = hotState.comment + 1;
    }
    else if (action === 3 /* EPhotoAction.DelComment */) {
        newState.comment = Math.max(0, hotState.comment - 1);
    }
    else {
        // do nothing
    }
    return newState;
}
function getHotFromState(hotState) {
    return hotState.like + hotState.comment;
}
async function updatePhotoHotState(photo, action) {
    const hotState = formatHotState(photo);
    const newState = getNewHotState(hotState, action);
    const newHot = getHotFromState(newState);
    const ret = await models_1.MarriagePhotoWallPhotoModel.updateById(photo.id, { hotState: JSON.stringify(newState), hot: newHot });
    return ret;
}
async function updateCommentLikeCount(comment, action) {
    const newLikeCount = action === 0 /* ECommentAction.Like */ ? comment.likeCount + 1 : Math.max(0, comment.likeCount - 1);
    const ret = await models_1.MarriagePhotoWallCommentModel.updateById(comment.id, { likeCount: newLikeCount });
    return ret;
}
async function addNotificationByAction(type, props) {
    if (props.roleId !== props.targetId) {
        const insertProps = buildNotificationByAction(type, props);
        await models_1.MarriagePhotoWallNotificationModel.insert(insertProps);
        await (0, redDot_1.marriagePhotoWallRedDotEnable)(props.targetId);
    }
}
function buildNotificationByAction(type, props) {
    const insertProps = {
        roleId: props.roleId,
        type,
        targetId: props.targetId,
        text: props.text || "",
        relateId: props.relateId || 0,
        photoId: props.photoId,
        wallId: props.wallId,
        status: 0 /* Statues.Normal */,
        createTime: Date.now(),
    };
    return insertProps;
}
async function addNotifications(notifications) {
    const realInsert = notifications.filter((r) => r.roleId !== r.targetId);
    if (realInsert && realInsert.length > 0) {
        await models_1.MarriagePhotoWallNotificationModel.insertBatch(realInsert);
        for (let item of realInsert) {
            await (0, redDot_1.marriagePhotoWallRedDotEnable)(item.targetId);
        }
    }
}
async function getMarriagePhotoWallNotificationNumInner(roleId) {
    const cnt = await models_1.MarriagePhotoWallNotificationModel.count({ targetId: roleId, status: 0 /* Statues.Normal */ });
    return cnt;
}
async function getRoleInfoFetcher(roleIds, curRoleId) {
    const roleInfoMap = await ProfileService.getRoleInfo(roleIds, curRoleId);
    const getRoleInfo = (roleId) => (roleInfoMap[roleId] ? roleInfoMap[roleId] : null);
    return getRoleInfo;
}
function auditMarriagePhotoWallPhoto(roleId, photoUrl, photoId, ip) {
    const picId = (0, imageAudit_1.genPicIdFromInfo)({ type: "marriage_photo_wall_photo", id: "" + photoId });
    (0, sendImageAudit_1.sendPic)([photoUrl], {
        roleId: roleId,
        picId: picId,
        media: constants_1.EPicMediaType.Image,
        ip: ip,
    });
    return picId;
}
const AuditImageUrl = "http://hi-163-nsh.nosdn.127.net/assert/images/guild_photo_wall_audit.png";
const AuditImageUrlHighRes = "http://hi-163-nsh.nosdn.127.net/assert/images/guild_image_detail_auditing.png";
function getPhotoUrl(url, auditStatus, isUserSelf, isHighRes = false) {
    if (config_1.testCfg.skip_photo_wall_audit) {
        return url;
    }
    if (auditStatus === constants_1.EAuditStatus.Reject) {
        return "http://hi-163-nsh.nosdn.127.net/assert/images/guild_photo_wall_reject.png";
    }
    else if ((0, util_1.contains)([constants_1.EAuditStatus.Init, constants_1.EAuditStatus.Auditing], auditStatus)) {
        if (isUserSelf) {
            return url;
        }
        else {
            return isHighRes ? AuditImageUrlHighRes : AuditImageUrl;
        }
    }
    else {
        return url;
    }
}
function getAuditStatus(auditStatus) {
    if (config_1.testCfg.skip_photo_wall_audit) {
        return constants_1.EAuditStatus.PASS;
    }
    return auditStatus;
}
/*
 这个可能需要改一下，直接用marriageLevel控制照片墙的数量就行，比如marriageLevel为5就表示上限5面墙
*/
function getNormalWallUpperBound(marriageLevel) {
    return marriageLevel;
}
async function fixPhotoWallDuplicate() {
    const rows = await findPhotoWallDuplicate();
    if (rows && rows.length > 0) {
        logger.info(rows, "FindPhotoWallDuplicate");
        const ids = rows.map(r => r.id);
        const delRet = await models_1.MarriagePhotoWallModel.deleteByIds(ids);
        logger.info({ ids, delRet }, "PhotoWallDuplicateDelete");
    }
    else {
        logger.info({ rows }, "CheckPhotoWallDuplicateEmpty");
    }
}
async function findPhotoWallDuplicate() {
    const query = models_1.MarriagePhotoWallModel.raw(`SELECT t1.* FROM nsh_marriage_photo_wall t1 INNER JOIN (
    SELECT marriageId, idx, MIN(id) AS minId FROM nsh_marriage_photo_wall GROUP BY marriageId, idx HAVING COUNT(*) > 1) t2
    ON t1.marriageId = t2.marriageId AND t1.idx = t2.idx AND t1.id > t2.minId ORDER BY t1.marriageId, t1.idx;`);
    const rows = await models_1.MarriagePhotoWallModel.executeByQuery(query);
    return rows;
}
//# sourceMappingURL=service.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CertifiedPlayerMaxNum = exports.MaxHandPickWallSize = exports.OpenHandPickLimit = exports.MaxAtRoleIdSize = exports.MaxPhotosWallPerMarriage = exports.MaxMarriageLevel = exports.MinMarriageLevel = exports.MarriagePhotoWallTable = exports.MaxPhotosSizePerWall = exports.HandPickWallId = void 0;
exports.HandPickWallId = "handpick";
exports.MaxPhotosSizePerWall = 50;
exports.MarriagePhotoWallTable = [
    { MarriageLevel: 1, newTemplate: 1, quality: 'low', wallLimit: 1, photoLimit: 8 },
    { MarriageLevel: 2, newTemplate: 0, quality: 'low', wallLimit: 1, photoLimit: 8 },
    { MarriageLevel: 3, newTemplate: 1, quality: 'low', wallLimit: 2, photoLimit: 16 },
    { MarriageLevel: 4, newTemplate: 1, quality: 'low', wallLimit: 3, photoLimit: 24 },
    { MarriageLevel: 5, newTemplate: 1, quality: 'medium', wallLimit: 3, photoLimit: 30 },
    { MarriageLevel: 6, newTemplate: 1, quality: 'medium', wallLimit: 3, photoLimit: 30 },
    { MarriageLevel: 7, newTemplate: 1, quality: 'high', wallLimit: 3, photoLimit: 36 },
];
exports.MinMarriageLevel = 1;
exports.MaxMarriageLevel = 7;
exports.MaxPhotosWallPerMarriage = 3;
exports.MaxAtRoleIdSize = 10;
exports.OpenHandPickLimit = {
    MinMarriageLevel: 0,
    MinPhotoCnt: 0
};
exports.MaxHandPickWallSize = 1;
exports.CertifiedPlayerMaxNum = 1000;
//# sourceMappingURL=constant.js.map
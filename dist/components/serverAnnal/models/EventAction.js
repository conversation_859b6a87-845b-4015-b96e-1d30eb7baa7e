"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerAnnalEventActionModel = exports.Action = void 0;
const BaseModel2_1 = require("../../../models/BaseModel2");
const constants_1 = require("../../../common/constants");
var Action;
(function (Action) {
    Action[Action["UpVote"] = 1] = "UpVote";
    Action[Action["DownVote"] = 2] = "DownVote";
})(Action = exports.Action || (exports.Action = {}));
class ServerAnnalEventActionClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.serverAnnalEventAction);
    }
}
exports.ServerAnnalEventActionModel = new ServerAnnalEventActionClass();
//# sourceMappingURL=EventAction.js.map
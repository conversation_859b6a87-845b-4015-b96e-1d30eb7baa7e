"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    ServerAnnalEventUpvote: {
        roleid: { type: Number },
        id: { type: Number },
        undo: { type: Boolean, required: false },
    },
    ServerAnnalEventDownvote: {
        roleid: { type: Number },
        id: { type: Number },
        undo: { type: Boolean, required: false },
    },
    GetServerAnnalEventList: {
        roleid: { type: Number },
        serverId: { type: Number },
        category: { type: Number, required: false, values: [1, 2, 3] },
        kw: { type: String, required: false },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    ServerAnnalScore: {
        roleid: { type: Number },
        week: { type: String, required: false },
    },
    GetServerAnnalFengyunPlayers: {
        serverId: { type: Number },
        size: { type: Number, max: 10 },
        week: { type: String, required: false },
    },
    SyncServerAnnal: {
        uuid: { type: String },
        serverId: { type: Number },
        eventType: { type: Number },
        eventTime: { type: Number },
        eventArgs: {
            type: Object,
            props: {
                playerId: { type: Number, required: false },
                playerIdList: { type: Array, required: false, items: { type: Number } },
                playerName: { type: String, required: false },
                playerNameList: { type: Array, required: false, items: { type: String } },
                guildId: { type: Number, required: false },
                guildName: { type: String, required: false },
                targetGuildId: { type: Number, required: false },
                targetGuildName: { type: String, required: false },
                first: { type: Number, required: false, values: [0, 1] },
                condIndex: { type: Number, required: false },
            },
        },
    },
};
//# sourceMappingURL=type.js.map
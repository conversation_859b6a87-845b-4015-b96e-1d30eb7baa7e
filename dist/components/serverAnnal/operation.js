"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getServerAnnalEventList = getServerAnnalEventList;
exports.incrPlayerEventScore = incrPlayerEventScore;
exports.incrPlayerEventScoreAfterSync = incrPlayerEventScoreAfterSync;
exports.getScoreIndex = getScoreIndex;
exports.syncServerAnnal = syncServerAnnal;
exports.serverAnnalScore = serverAnnalScore;
exports.getServerAnnalFengyunPlayers = getServerAnnalFengyunPlayers;
exports.doEventAction = doEventAction;
exports.undoEventAction = undoEventAction;
exports.incrActionCnt = incrActionCnt;
exports.descActionCnt = descActionCnt;
exports.processEventAction = processEventAction;
exports.checkEventExist = checkEventExist;
exports.upVoteServerAnnalEvent = upVoteServerAnnalEvent;
exports.downVoteServerAnnalEvent = downVoteServerAnnalEvent;
exports.getIsUpVoteSet = getIsUpVoteSet;
const _ = require("lodash");
const constants_1 = require("../../common/constants");
const bluebird = require("bluebird");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const util_1 = require("../../common/util");
const tableDef_1 = require("./services/tableDef");
const player_1 = require("../../services/player");
const serverTransfer_1 = require("../../services/serverTransfer");
const models_1 = require("../../models");
const serverList_1 = require("../../services/serverList");
const cacheUtil_1 = require("../../services/cacheUtil");
const logger = logger_1.apiLogger.child({ clazz: "serverAnnalOperations" });
const Action = constants_1.ServerAnnalAction;
const errorActionMap = {
    [Action.UpVote]: {
        alreadyDo: errorCodes_1.ServerAnnalErrors.AlreadyUpVote,
        notDo: errorCodes_1.ServerAnnalErrors.NotUpVote,
    },
    [Action.DownVote]: {
        alreadyDo: errorCodes_1.ServerAnnalErrors.AlreadyDownVote,
        notDo: errorCodes_1.ServerAnnalErrors.NotDownVote,
    },
};
function keep2MonthScope(query, date) {
    return query.where("CreateTime", ">", date.getTime() - 2 * 30 * constants_1.ONE_DAY_SECONDS * 1000);
}
async function getVoteStat(ids) {
    const rows = await models_1.ServerAnnalEventModel.find({ ID: ids }, { cols: ["ID", "UpVote", "DownVote"] });
    return (0, util_1.keyToRecordMap)(rows, "ID");
}
/** 获取编年史事件列表 */
async function getServerAnnalEventList(params) {
    const ret = {
        list: [],
    };
    let query = models_1.ServerAnnalEventModel.normalScope();
    if (params.category === tableDef_1.ECategory.HighLight) {
        query = query.orderBy("First", "desc").orderBy("ID", "asc");
    }
    else {
        query = query.orderBy("ID", "desc");
    }
    if (params.serverId) {
        const mergeInfo = await (0, serverList_1.getMergeServerInfo)(params.serverId);
        const serverIds = mergeInfo.serverIds;
        query = query.whereIn("ServerId", serverIds);
    }
    if (params.category) {
        query = query.where("Category", params.category);
    }
    if (params.kw) {
        const kwCols = ["PlayerIds", "PlayerNames", "GuildIds", "GuildNames"];
        query = query.andWhere(function () {
            for (const col of kwCols) {
                this.orWhere(col, "like", `%${params.kw}%`);
            }
        });
    }
    if (params.category !== tableDef_1.ECategory.HighLight) {
        query = keep2MonthScope(query, new Date());
    }
    query = models_1.ServerAnnalEventModel.paginationScope(query, { page: params.page, pageSize: params.pageSize });
    const rows = await models_1.ServerAnnalEventModel.cachableQuery(query, models_1.ServerAnnalEventCols, {
        kp: "server_annal_list",
        cacheObj: params,
    });
    const ids = rows.map((r) => r.ID);
    const statMap = await getVoteStat(ids);
    const isVoteUpSet = await getIsUpVoteSet(params.roleid, ids);
    const playerIds = _.uniq(_.flatMap(rows, (r) => (0, util_1.csvStrToIntArray)(r.PlayerIds)));
    const transPlayerIdMap = await (0, serverTransfer_1.getLatestTransferRoleIdsMap)(playerIds);
    ret.list = rows.map((r) => {
        const playerIds = (0, util_1.csvStrToIntArray)(r.PlayerIds).map((id) => transPlayerIdMap.get(id));
        const names = (0, util_1.csvStrToArray)(r.PlayerNames);
        const players = _.zipWith(playerIds, names, (roleId, roleName) => {
            return { roleId, roleName };
        });
        const stat = statMap.get(r.ID) || { UpVote: 0, DownVote: 0 };
        const isUpVote = isVoteUpSet.has(r.ID);
        let eventArgs = JSON.parse(r.EventArgs);
        eventArgs = formatPlayerIdsInEventArgs(eventArgs, transPlayerIdMap);
        return {
            id: r.ID,
            category: r.Category,
            serverId: r.ServerId,
            eventType: r.EventType,
            eventTime: r.EventTime,
            eventArgs: eventArgs,
            upvote: stat.UpVote,
            isUpVote: isUpVote,
            players: players,
        };
    });
    return ret;
}
async function incrPlayerEventScore(week, roleId, serverId, incr, updateTime, battleScore) {
    if (incr <= 0)
        return;
    const createProps = {
        RoleId: roleId,
        ServerId: serverId,
        Score: incr,
        Week: week,
        UpdateTime: updateTime,
        BattleScore: battleScore || 0,
    };
    const r = await models_1.ServerAnnalFengyunModel.findOne({ Week: week, RoleId: roleId });
    if (r && r.ID) {
        const newScore = r.Score + incr;
        const upProps = {
            Score: newScore,
            UpdateTime: updateTime,
        };
        // 当且仅当battleScore传入且大于0的时候, 才更新battleScore值， 游戏那边不能确保每次battleScore都在每次事件会同步
        if (battleScore && battleScore > 0) {
            upProps.BattleScore = battleScore;
        }
        const upRet = await models_1.ServerAnnalFengyunModel.updateById(r.ID, upProps);
        logger.info({ week, roleId, upProps, rowsAffected: upRet.affectedRows }, "UpdateServerAnnalFengyunOk");
    }
    else {
        // check last transfer roleId for same server and inherent score
        const preTransferRoleId = await (0, serverTransfer_1.findLastTransferSameServerRoleId)(roleId, serverId);
        if (preTransferRoleId) {
            logger.info({ week, roleId, preTransferRoleId, incr }, "FindLastTransferSameServerRoleId");
            const r = await models_1.ServerAnnalFengyunModel.findOne({ Week: week, RoleId: preTransferRoleId });
            if (r && r.ID) {
                createProps.Score = createProps.Score + r.Score;
                const fyId = await models_1.ServerAnnalFengyunModel.insert(createProps);
                logger.info({ week, roleId, preTransferRoleId, incr, inherentFengyun: r, fyId }, "IncrPlayerEventAndInherentScoreOk");
            }
        }
        else {
            const id = await models_1.ServerAnnalFengyunModel.insert(createProps);
            logger.info({ week, roleId, incr, id }, "IncrPlayerEventScoreOk");
        }
    }
}
async function incrPlayerEventScoreAfterSync(playerIds, r, battleScoreMap) {
    for (const playerId of playerIds) {
        const battleScore = battleScoreMap.get(playerId) || 0;
        // 需求临时改过，不在有week的概念，都是累计积分，这里用all来设置week字段来保持表结构兼容
        await incrPlayerEventScore(constants_1.ServerAnnalAllWeek, playerId, r.ServerId, r.Score, r.EventTime, battleScore);
    }
}
function convertToRelatedPlayers(params) {
    const list = [];
    if (params.eventArgs) {
        const eventArgs = params.eventArgs;
        if (eventArgs.playerId) {
            list.push({ roleId: eventArgs.playerId, roleName: eventArgs.playerName });
        }
        else if (eventArgs.playerIdList) {
            for (let i = 0; i < eventArgs.playerIdList.length; i++) {
                const roleId = eventArgs.playerIdList[i];
                const roleName = eventArgs.playerNameList[i];
                list.push({ roleId, roleName });
            }
        }
    }
    return list;
}
function convertToRelatedGuilds(params) {
    const list = [];
    if (params.eventArgs) {
        const eventArgs = params.eventArgs;
        if (eventArgs.guildId) {
            list.push({ guildId: eventArgs.guildId, guildName: eventArgs.guildName });
        }
        if (eventArgs.targetGuildId) {
            list.push({ guildId: eventArgs.targetGuildId, guildName: eventArgs.targetGuildName });
        }
    }
    return list;
}
function convertToBattleScores(playerIds, params) {
    const map = new Map();
    if (!params.eventArgs)
        return map;
    const eventArgs = params.eventArgs;
    if (playerIds.length === 1) {
        // 1个元素， 战力在
        if (eventArgs.playerScore > 0) {
            map.set(playerIds[0], eventArgs.playerScore || 0);
        }
    }
    else {
        if (eventArgs.playerScoreList) {
            for (let i = 0; i < playerIds.length; i++) {
                if (eventArgs.playerScoreList && eventArgs.playerScoreList[i] > 0) {
                    map.set(playerIds[i], eventArgs.playerScoreList[i]);
                }
            }
        }
    }
    return map;
}
function getIsFirstStatus(eventArgs) {
    if (eventArgs && eventArgs.first) {
        return eventArgs.first;
    }
    else {
        return 0;
    }
}
function getScoreIndex(eventArgs) {
    // get the correct condIndex
    let scoreIndex = 0;
    if (eventArgs && eventArgs.condIndex) {
        // 游戏客户端傻乎乎的从1开始数数, 这里处理成index
        scoreIndex = Math.max(0, eventArgs.condIndex - 1);
    }
    return scoreIndex;
}
/** 游戏服务器同步编年史事件 (IP白名单授权) */
async function syncServerAnnal(params) {
    const { eventType } = params;
    const cat = (0, tableDef_1.getCategoryByEventId)(eventType);
    const first = getIsFirstStatus(params.eventArgs);
    const scoreIndex = getScoreIndex(params.eventArgs);
    const score = (0, tableDef_1.getScoreByEventId)(eventType, scoreIndex);
    const week = (0, util_1.getSunWeekStartDayStr)(new Date(params.eventTime));
    const createProps = {
        UUID: params.uuid,
        ServerId: params.serverId,
        EventType: params.eventType,
        EventTime: params.eventTime,
        EventArgs: JSON.stringify(params.eventArgs),
        Category: cat,
        UpVote: 0,
        DownVote: 0,
        PlayerIds: "",
        PlayerNames: "",
        GuildIds: "",
        GuildNames: "",
        Status: 0,
        Score: score,
        Week: week,
        First: first,
        CreateTime: Date.now(),
    };
    const players = convertToRelatedPlayers(params);
    const playerIds = players.map((r) => r.roleId);
    const guilds = convertToRelatedGuilds(params);
    if (players) {
        createProps.PlayerIds = (0, util_1.arrayToCsv)(players.map((p) => p.roleId));
        createProps.PlayerNames = (0, util_1.arrayToCsv)(players.map((p) => p.roleName));
    }
    if (guilds) {
        createProps.GuildIds = (0, util_1.arrayToCsv)(guilds.map((p) => p.guildId));
        createProps.GuildNames = (0, util_1.arrayToCsv)(guilds.map((p) => p.guildName));
    }
    const updateProps = _.omit(createProps, ["UUID", "CreateTime"]);
    const info = await models_1.ServerAnnalEventModel.createOrUpdate(createProps, updateProps);
    logger.info({ createProps, updateProps }, "syncServerAnnal");
    const ret = { insertId: info.insertId, affectedRows: info.affectedRows };
    const battleScoreMap = convertToBattleScores(playerIds, params);
    await incrPlayerEventScoreAfterSync(playerIds, createProps, battleScoreMap);
    return ret;
}
/** 获取个人累计积分数据 */
async function serverAnnalScore(params) {
    const { roleid } = params;
    const query = models_1.ServerAnnalFengyunModel.scope().where({ RoleId: params.roleid, Week: constants_1.ServerAnnalAllWeek }).limit(1);
    const rows = await models_1.ServerAnnalFengyunModel.cachableQuery(query, ["Score"], {
        expire: 60,
        kp: "server_annal_score",
        cacheObj: { roleid },
    });
    const score = rows.length > 0 ? rows[0].Score : 0;
    const data = { score };
    return data;
}
async function getServerAnnalFengyunPlayersFromDB(serverId, size) {
    const result = { list: [] };
    let query = models_1.ServerAnnalFengyunModel.scope()
        .where({ Week: constants_1.ServerAnnalAllWeek })
        .orderBy("Score", "desc")
        .orderBy("UpdateTime", "asc")
        .orderBy("BattleScore", "desc")
        .limit(size);
    if (serverId) {
        const mergeInfo = await (0, serverList_1.getMergeServerInfo)(serverId);
        const serverIds = mergeInfo.serverIds;
        query = query.whereIn("ServerId", serverIds).whereNotIn("RoleId", models_1.TransferModel.scope().select("OldId"));
    }
    const cols = models_1.ServerAnnalFengyunCols;
    const rows = await models_1.ServerAnnalFengyunModel.cachableQuery(query, cols, {
        kp: "server_annal_fengyun",
        cacheObj: { serverId, size },
        expire: 60,
    });
    const roleIds = rows.map((r) => r.RoleId);
    const basicInfoMap = await (0, player_1.getBasicInfoMap)(roleIds);
    let rank = 1;
    result.list = rows.map((r) => {
        const tinyInfo = basicInfoMap.get(r.RoleId) || { guildId: 0, guildName: "", roleName: "" };
        const item = {
            id: r.ID,
            rank: rank,
            week: r.Week,
            roleId: r.RoleId,
            roleName: tinyInfo.roleName,
            serverId: r.ServerId,
            guild: { id: tinyInfo.guildId, name: tinyInfo.guildName },
            score: r.Score,
        };
        rank += 1;
        return item;
    });
    return result;
}
class ServerAnnalFengyunPlayersCacheClass extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 60;
    }
    getKey(params) {
        return (0, util_1.cacheKeyGen)("server_annal_fengyun_players", { serverId: params.serverId, size: params.size });
    }
    fetchDataSource(params) {
        return getServerAnnalFengyunPlayersFromDB(params.serverId, params.size);
    }
}
const ServerAnnalFengyunPlayersCache = new ServerAnnalFengyunPlayersCacheClass();
/** 风云人物 */
async function getServerAnnalFengyunPlayers(params) {
    return ServerAnnalFengyunPlayersCache.get(params);
}
async function doEventAction(roleId, eventId, action) {
    const r = await models_1.ServerAnnalEventActionModel.findOne({ RoleId: roleId, EventId: eventId, Action: action });
    if (r) {
        if (r.Status === 0 /* Statues.Normal */) {
            await bluebird.reject(errorActionMap[action].alreadyDo);
        }
        else {
            // update
            const ret = await models_1.ServerAnnalEventActionModel.updateById(r.ID, { Status: 0 /* Statues.Normal */ });
            return ret;
        }
    }
    else {
        // insert
        const id = await models_1.ServerAnnalEventActionModel.insert({
            EventId: eventId,
            RoleId: roleId,
            Action: action,
            CreateTime: Date.now(),
            Status: 0 /* Statues.Normal */,
        });
        return { insertId: id, affectedRows: 1 };
    }
}
async function undoEventAction(roleId, eventId, action) {
    const r = await models_1.ServerAnnalEventActionModel.findOne({ RoleId: roleId, EventId: eventId });
    if (r && r.Status === 0 /* Statues.Normal */) {
        const ret = await models_1.ServerAnnalEventActionModel.softDeleteById(r.ID);
        return ret;
    }
    else {
        await bluebird.reject(errorActionMap[action].notDo);
    }
}
function getColNameByAction(action) {
    if (action === constants_1.ServerAnnalAction.UpVote) {
        return "UpVote";
    }
    else {
        return "DownVote";
    }
}
async function incrActionCnt(id, action) {
    const colName = getColNameByAction(action);
    const query = models_1.ServerAnnalEventModel.scope().increment(colName, 1).where("ID", id);
    return models_1.ServerAnnalEventModel.executeByQuery(query);
}
async function descActionCnt(id, action) {
    const colName = getColNameByAction(action);
    const query = models_1.ServerAnnalEventModel.scope().where("id", id).decrement(colName, 1).where(colName, ">", 0);
    return models_1.ServerAnnalEventModel.executeByQuery(query);
}
async function processEventAction(roleId, eventId, action, undo) {
    await checkEventExist(eventId);
    let ret = null;
    if (undo) {
        ret = await undoEventAction(roleId, eventId, action);
        descActionCnt(eventId, action);
    }
    else {
        ret = await doEventAction(roleId, eventId, action);
        incrActionCnt(eventId, action);
    }
    return { id: ret.insertId, affectedRows: ret.affectedRows };
}
async function checkEventExist(eventId) {
    const isExist = await models_1.ServerAnnalEventModel.existsById(eventId);
    if (!isExist) {
        return Promise.reject(errorCodes_1.ServerAnnalErrors.EntityNotFound);
    }
}
async function upVoteServerAnnalEvent(params) {
    return processEventAction(params.roleid, params.id, Action.UpVote, params.undo);
}
async function downVoteServerAnnalEvent(params) {
    const data = await processEventAction(params.roleid, params.id, Action.DownVote, params.undo);
    return data;
}
async function getIsUpVoteSet(roleId, ids) {
    const rows = await models_1.ServerAnnalEventActionModel.find({ RoleId: roleId, Action: Action.UpVote, EventId: ids, Status: 0 /* Statues.Normal */ }, { cols: ["EventId"] });
    const voteUpIds = rows.map((r) => r.EventId);
    return new Set(voteUpIds);
}
/** set roleId inPlace */
function formatPlayerIdsInEventArgs(eventArgs, map) {
    if (eventArgs) {
        if (eventArgs.playerId) {
            return eventArgs;
        }
        else if (eventArgs.playerIdList) {
            for (let i = 0; i < eventArgs.playerIdList.length; i++) {
                const id = eventArgs.playerIdList[i];
                eventArgs.playerIdList[i] = map.get(id);
            }
        }
    }
    return eventArgs;
}
//# sourceMappingURL=operation.js.map
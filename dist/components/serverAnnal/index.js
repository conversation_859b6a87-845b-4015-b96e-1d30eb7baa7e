"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerAnnalComponent = exports.paths = void 0;
const config_1 = require("../../common/config");
const helper_1 = require("../../helper");
const operation_1 = require("./operation");
const type_1 = require("./type");
const gameIpLimit = (0, helper_1.ipLimitChecker)(config_1.GAME_IPS);
exports.paths = [
    {
        method: "get",
        url: "/server_annal/list",
        paramsSchema: type_1.ReqSchemas.GetServerAnnalEventList,
        operation: operation_1.getServerAnnalEventList,
    },
    {
        method: "get",
        url: "/server_annal/score",
        paramsSchema: type_1.ReqSchemas.ServerAnnalScore,
        operation: operation_1.serverAnnalScore,
    },
    {
        method: "get",
        url: "/server_annal/fengyun_players",
        paramsSchema: type_1.ReqSchemas.GetServerAnnalFengyunPlayers,
        operation: operation_1.getServerAnnalFengyunPlayers,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/server_annal/sync",
        paramsSchema: type_1.ReqSchemas.SyncServerAnnal,
        before: gameIpLimit,
        operation: operation_1.syncServerAnnal,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/server_annal/event/upvote",
        paramsSchema: type_1.ReqSchemas.ServerAnnalEventUpvote,
        operation: operation_1.upVoteServerAnnalEvent,
    },
    {
        method: "post",
        url: "/server_annal/event/downvote",
        paramsSchema: type_1.ReqSchemas.ServerAnnalEventDownvote,
        operation: operation_1.downVoteServerAnnalEvent,
    },
];
exports.ServerAnnalComponent = {
    paths: exports.paths,
    prefix: "/server_annal",
};
//# sourceMappingURL=index.js.map
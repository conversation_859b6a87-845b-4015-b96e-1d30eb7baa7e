"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ECategory = void 0;
exports.getCategoryByEventId = getCategoryByEventId;
exports.getScoreByEventId = getScoreByEventId;
const util_1 = require("../../../common/util");
const project_1 = require("../../../project");
const path = require("path");
var ECategory;
(function (ECategory) {
    ECategory[ECategory["HighLight"] = 1] = "HighLight";
    ECategory[ECategory["Personal"] = 2] = "Personal";
    ECategory[ECategory["Guild"] = 3] = "Guild";
    ECategory[ECategory["Init"] = 0] = "Init";
})(ECategory || (exports.ECategory = ECategory = {}));
function getAllEvents() {
    const list = require(path.join((0, project_1.getAssetsDir)(), "./json/serverAnnalTiny.json"));
    return (0, util_1.keyToRecordMap)(list, "id");
}
function getCategoryByEventId(eventId) {
    const map = getAllEvents();
    const item = map.get(eventId);
    if (item) {
        const result = item.category;
        return result;
    }
    else {
        // 未命中策划表是，使用正则匹配来确定
        const eIdStr = "" + eventId;
        const m = eIdStr.match(/(\d)\d\d\d/);
        if (m) {
            return parseInt(m[1]);
        }
        else {
            return 0;
        }
    }
}
function getScoreByEventId(eventId, scoreIndex) {
    const data = getAllEvents();
    const scores = (data.get(eventId) || { scores: [0] }).scores;
    const index = Math.min(Math.max(scoreIndex, 0), scores.length - 1); // when index is overflow use the last score
    return scores[index] || 0;
}
//# sourceMappingURL=tableDef.js.map
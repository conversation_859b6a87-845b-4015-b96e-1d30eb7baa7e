"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGmGbSuperAasLimitGameidUrsAccount = getGmGbSuperAasLimitGameidUrsAccount;
exports.postGmGbSuperAasLimitGameidUrsAccount = postGmGbSuperAasLimitGameidUrsAccount;
exports.deleteGmGbSuperAasLimitGameidUrsAccount = deleteGmGbSuperAasLimitGameidUrsAccount;
exports.gmGbSuperAasLimitUrsAccount = gmGbSuperAasLimitUrsAccount;
const _ = require("lodash");
const gbSuperAssLimit_1 = require("../fcm/gbSuperAssLimit");
/** [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#5.-%E6%9F%A5%E8%AF%A2%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99) */
async function getGmGbSuperAasLimitGameidUrsAccount(params) {
    return gbSuperAssLimit_1.GbSuperAssLimitApi.create(params.gameid, params.account).rawGet();
}
/** [计费对应上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#3.-%E6%B7%BB%E5%8A%A0(%E6%88%96%E6%9B%B4%E6%96%B0)%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99) */
async function postGmGbSuperAasLimitGameidUrsAccount(params) {
    const body = _.omit(params, ['gameid', 'account']);
    return gbSuperAssLimit_1.GbSuperAssLimitApi.create(params.gameid, params.account).post(body);
}
/** [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#4-%E5%88%A0%E9%99%A4%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99) */
async function deleteGmGbSuperAasLimitGameidUrsAccount(params) {
    return gbSuperAssLimit_1.GbSuperAssLimitApi.create(params.gameid, params.account).delete();
}
/** 批量查询防沉迷规则 */
async function gmGbSuperAasLimitUrsAccount(params) {
    return gbSuperAssLimit_1.GbSuperAssLimitApi.create("", params.account).getBatch();
}
//# sourceMappingURL=operation.js.map
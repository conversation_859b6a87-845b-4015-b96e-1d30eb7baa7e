"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmGbSuperAssLimitComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/gm/gb_super_aas_limit/:gameid/urs/:account",
        paramsSchema: type_1.ReqSchemas.GetGmGbSuperAasLimitGameidUrsAccount,
        operation: operation_1.getGmGbSuperAasLimitGameidUrsAccount,
    },
    {
        method: "post",
        url: "/gm/gb_super_aas_limit/:gameid/urs/:account",
        paramsSchema: type_1.ReqSchemas.PostGmGbSuperAasLimitGameidUrsAccount,
        operation: operation_1.postGmGbSuperAasLimitGameidUrsAccount,
    },
    {
        method: "del",
        url: "/gm/gb_super_aas_limit/:gameid/urs/:account",
        paramsSchema: type_1.ReqSchemas.DeleteGmGbSuperAasLimitGameidUrsAccount,
        operation: operation_1.deleteGmGbSuperAasLimitGameidUrsAccount,
    },
    {
        method: "get",
        url: "/gm/gb_super_aas_limit/urs/:account",
        paramsSchema: type_1.ReqSchemas.GmGbSuperAasLimitUrsAccount,
        operation: operation_1.gmGbSuperAasLimitUrsAccount,
    },
];
exports.GmGbSuperAssLimitComponent = {
    paths: exports.paths,
    prefix: "/gm/gb_super_aas_limit/{gameid}/urs/",
};
//# sourceMappingURL=index.js.map
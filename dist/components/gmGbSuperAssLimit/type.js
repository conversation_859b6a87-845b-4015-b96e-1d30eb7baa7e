"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    GetGmGbSuperAasLimitGameidUrsAccount: { type: "object", properties: {}, required: [] },
    PostGmGbSuperAasLimitGameidUrsAccount: {
        type: "object",
        properties: {
            create_order_limit: { type: "number", minimum: 0, maximum: ********* },
            month_sum_limit: { type: "number", minimum: 0, maximum: ********* },
            currency: { type: "string", enum: ["CNY"] },
            online_time_limit: { type: "number" },
            holiday_online_time_limit: { type: "number" },
            curfew_start_time: { type: "string" },
            curfew_end_time: { type: "string" },
            effective_time: { type: "string" },
            expired_time: { type: "string" },
            aas_msg: { type: "string" },
            source: { type: "integer", enum: [0, 1] },
        },
        required: ["effective_time", "aas_msg", "source"],
    },
    DeleteGmGbSuperAasLimitGameidUrsAccount: { type: "object", properties: {}, required: [] },
    GmGbSuperAasLimitUrsAccount: { type: "object", properties: {}, required: [] },
};
//# sourceMappingURL=type.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    NoteMailMailBoxTypeShow: {
        mail_box_type: { type: String, values: ["inbox", "outbox"] },
        roleid: { type: Number },
        id: { type: Number },
    },
    NoteMailMailBoxTypeDel: {
        mail_box_type: { type: String, values: ["inbox", "outbox"] },
        roleid: { type: Number },
        id: { type: Number },
    },
    NoteMailInboxList: {
        roleid: { type: Number },
        kw: { type: String, required: false },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    NoteMailOutboxList: {
        roleid: { type: Number },
        kw: { type: String, required: false },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    NoteMailMailBoxTypeStar: {
        mail_box_type: { type: String, values: ["inbox", "outbox"] },
        roleid: { type: Number },
        id: { type: Number },
    },
    NoteMailMailBoxTypeUnstar: {
        mail_box_type: { type: String, values: ["inbox", "outbox"] },
        roleid: { type: Number },
        id: { type: Number },
    },
    NoteMailSend: {
        sender: { type: Number },
        receiver: { type: Number },
        receiverName: { type: String },
        content: { type: String },
        signature: { type: String },
        sendTime: { type: Number },
        style: { type: Number },
    },
    NoteMailCleanAll: {
        roleid: { type: Number },
    },
};
//# sourceMappingURL=type.js.map
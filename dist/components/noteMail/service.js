"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNewNoteMailNum = void 0;
exports.getNewNoteMailNumFromDB = getNewNoteMailNumFromDB;
exports.refreshNewNoteMailCache = refreshNewNoteMailCache;
exports.lastViewTimeKey = lastViewTimeKey;
exports.markLastViewTime = markLastViewTime;
exports.getLastViewTime = getLastViewTime;
const cacheService_1 = require("../../common/cacheService");
const config_1 = require("../../common/config");
const redis_1 = require("../../common/redis");
const util_1 = require("../../common/util");
const models_1 = require("../../models");
async function getNewNoteMailNumFromDB(roleId) {
    if (config_1.Features.note_mail && config_1.noteMailCfg.redDot) {
        const lastViewTime = await getLastViewTime(roleId);
        if (lastViewTime === 0) {
            // this mean user never open note mail list ui
            return 0;
        }
        else {
            const query = models_1.NoteMailModel.normalScope().where("receiver", roleId).where("createTime", ">", lastViewTime);
            const cnt = await models_1.NoteMailModel.countByQuery(query);
            return cnt;
        }
    }
    else {
        return 0;
    }
}
function newNoteMailCacheKey(roleId) {
    return (0, util_1.cacheKeyGen)("note_mail_new_mail_num", { roleId });
}
exports.getNewNoteMailNum = (0, cacheService_1.smartMemorize)(getNewNoteMailNumFromDB, {
    keyGen: newNoteMailCacheKey,
    expireSeconds: 5 * 60,
});
async function refreshNewNoteMailCache(roleId) {
    const key = newNoteMailCacheKey(roleId);
    return (0, redis_1.getRedis)().delAsync(key);
}
function lastViewTimeKey(roleId) {
    return (0, util_1.cacheKeyGen)("note_mail_last_view_time", { roleId });
}
async function markLastViewTime(roleId) {
    const key = lastViewTimeKey(roleId);
    const ts = Date.now();
    await (0, redis_1.getRedis)().setAsync(key, ts, redis_1.ExpireType.EX, config_1.ONE_DAY_SECONDS * 60);
    return ts;
}
async function getLastViewTime(roleId) {
    const key = lastViewTimeKey(roleId);
    const ret = await (0, redis_1.getRedis)().getAsync(key);
    if (ret) {
        return parseInt(ret, 10);
    }
    else {
        return 0;
    }
}
//# sourceMappingURL=service.js.map
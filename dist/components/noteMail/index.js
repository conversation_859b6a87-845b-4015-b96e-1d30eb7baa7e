"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoteMailComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/note_mail/:mail_box_type/show",
        paramsSchema: type_1.ReqSchemas.NoteMailMailBoxTypeShow,
        operation: operation_1.noteMailMailBoxTypeShow,
    },
    {
        method: "post",
        url: "/note_mail/:mail_box_type/del",
        paramsSchema: type_1.ReqSchemas.NoteMailMailBoxTypeDel,
        operation: operation_1.noteMailMailBoxTypeDel,
    },
    {
        method: "get",
        url: "/note_mail/inbox/list",
        paramsSchema: type_1.ReqSchemas.NoteMailInboxList,
        operation: operation_1.noteMailInboxList,
    },
    {
        method: "get",
        url: "/note_mail/outbox/list",
        paramsSchema: type_1.ReqSchemas.NoteMailOutboxList,
        operation: operation_1.noteMailOutboxList,
    },
    {
        method: "post",
        url: "/note_mail/:mail_box_type/star",
        paramsSchema: type_1.ReqSchemas.NoteMailMailBoxTypeStar,
        operation: operation_1.noteMailInboxStar,
    },
    {
        method: "post",
        url: "/note_mail/:mail_box_type/unstar",
        paramsSchema: type_1.ReqSchemas.NoteMailMailBoxTypeUnstar,
        operation: operation_1.noteMailInboxUnstar,
    },
    {
        method: "post",
        url: "/note_mail/send",
        paramsSchema: type_1.ReqSchemas.NoteMailSend,
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.noteMailSend,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/note_mail/clean_all",
        paramsSchema: type_1.ReqSchemas.NoteMailCleanAll,
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.noteMailCleanAll,
        option: { skipSkey: true },
    },
];
exports.NoteMailComponent = {
    paths: exports.paths,
    prefix: "/note_mail/",
};
//# sourceMappingURL=index.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findMail = findMail;
exports.noteMailMailBoxTypeShow = noteMailMailBoxTypeShow;
exports.noteMailMailBoxTypeDel = noteMailMailBoxTypeDel;
exports.noteMailInboxList = noteMailInboxList;
exports.noteMailOutboxList = noteMailOutboxList;
exports.noteMailInboxStar = noteMailInboxStar;
exports.noteMailInboxUnstar = noteMailInboxUnstar;
exports.noteMailSend = noteMailSend;
exports.noteMailCleanAll = noteMailCleanAll;
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
const util_1 = require("../../common/util");
const RoleInfos_1 = require("../../models/RoleInfos");
const service_1 = require("./service");
const roleInfo_1 = require("../../services/roleInfo");
const logger_1 = require("../../logger");
const _ = require("lodash");
const logger = (0, logger_1.clazzLogger)("noteMail/operation");
async function findMail(id, roleid, boxType) {
    const statusCol = boxType === "inbox" ? "status" : "senderStatus";
    const roleIdCol = boxType === "inbox" ? "receiver" : "sender";
    const r = await models_1.NoteMailModel.findOne({ id, [roleIdCol]: roleid, [statusCol]: [0 /* Statues.Normal */, 1 /* Statues.Read */] });
    return r;
}
/** 查看收件箱便利贴详情 */
async function noteMailMailBoxTypeShow(params) {
    const { id, roleid, mail_box_type } = params;
    const r = await findMail(id, roleid, mail_box_type);
    if (r && r.id) {
        const star = r.starTime > 0;
        const isRead = true;
        const roleNameMap = await (0, roleInfo_1.getRoleNameMap)([r.sender, r.receiver]);
        const senderName = roleNameMap.get(r.sender);
        const receiverRoleName = roleNameMap.get(r.receiver);
        const data = Object.assign({}, r, { star, isRead, senderName, receiverRoleName });
        const statusCol = params.mail_box_type === "inbox" ? "status" : "senderStatus";
        models_1.NoteMailModel.updateById(r.id, { [statusCol]: 1 /* Statues.Read */ });
        return data;
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.NoteMailErrors.MailIdNotExist);
    }
}
/** 删除便利贴 */
async function noteMailMailBoxTypeDel(params) {
    const { id, roleid, mail_box_type } = params;
    const r = await findMail(id, roleid, mail_box_type);
    if (r && r.id) {
        const statusCol = params.mail_box_type === "inbox" ? "status" : "senderStatus";
        const ret = await models_1.NoteMailModel.updateById(r.id, { [statusCol]: -1 /* Statues.Deleted */ });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        return data;
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.NoteMailErrors.MailIdNotExist);
    }
}
async function formatInBoxList(list) {
    const roleIds = _.flatMap(list, r => [r.sender, r.receiver]);
    const roleNameMap = await (0, roleInfo_1.getRoleNameMap)(roleIds);
    const newList = list.map((r) => {
        const star = r.starTime > 0;
        const isRead = r.status === 1 /* Statues.Read */;
        const senderName = roleNameMap.get(r.sender);
        const receiverRoleName = roleNameMap.get(r.receiver);
        const showRecord = (0, util_1.omitBy)(r, ["senderStatus", "senderStarTime"]);
        const data = Object.assign({}, showRecord, { star, isRead, senderName, receiverRoleName });
        return data;
    });
    return newList;
}
async function formatOutBoxList(list) {
    for (let e of list) {
        e.status = e.senderStatus;
        e.starTime = e.senderStarTime;
    }
    return formatInBoxList(list);
}
/** 收件箱邮件列表 */
async function noteMailInboxList(params) {
    const { roleid, page, pageSize } = params;
    let query = models_1.NoteMailModel.scope()
        .from(`${models_1.NoteMailModel.tableName} as m`)
        .whereIn("m.status", [0 /* Statues.Normal */, 1 /* Statues.Read */])
        .where("m.receiver", roleid)
        .orderBy("m.starTime", "desc")
        .orderBy("m.id", "desc");
    const cols = models_1.NoteMailCols.map((c) => `m.${c}`);
    if (params.kw) {
        query = query
            .innerJoin(`${RoleInfos_1.RoleInfoModel.tableName} as r`, "r.RoleId", "m.sender")
            .where("r.RoleName", "like", `%${params.kw}%`);
    }
    const { list, meta } = await models_1.NoteMailModel.powerListQuery({
        initQuery: query,
        pagination: { page, pageSize },
        //@ts-ignore
        select: cols,
    });
    const newList = await formatInBoxList(list);
    const data = {
        list: newList,
        meta,
    };
    return data;
}
/** 发件箱邮件列表 */
async function noteMailOutboxList(params) {
    const { roleid, page, pageSize } = params;
    let query = models_1.NoteMailModel.scope()
        .from(`${models_1.NoteMailModel.tableName} as m`)
        .whereIn("m.senderStatus", [0 /* Statues.Normal */, 1 /* Statues.Read */])
        .where("m.sender", roleid)
        .orderBy("m.senderStarTime", "desc")
        .orderBy("m.id", "desc");
    const cols = models_1.NoteMailCols.map((c) => `m.${c}`);
    if (params.kw) {
        query = query
            .innerJoin(`${RoleInfos_1.RoleInfoModel.tableName} as r`, "r.RoleId", "m.receiver")
            .where("r.RoleName", "like", `%${params.kw}%`);
    }
    const { list, meta } = await models_1.NoteMailModel.powerListQuery({
        initQuery: query,
        pagination: { page, pageSize },
        //@ts-ignore
        select: cols,
    });
    const newList = await formatOutBoxList(list);
    const data = {
        list: newList,
        meta,
    };
    return data;
}
/** 收藏便利贴邮件 */
async function noteMailInboxStar(params) {
    const { id, roleid, mail_box_type } = params;
    const r = await findMail(id, roleid, mail_box_type);
    if (r && r.id) {
        const starCol = params.mail_box_type === "inbox" ? "starTime" : "senderStarTime";
        const ret = await models_1.NoteMailModel.updateById(r.id, { [starCol]: Date.now() });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        return data;
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.NoteMailErrors.MailIdNotExist);
    }
}
/** 取消收藏便利贴邮件 */
async function noteMailInboxUnstar(params) {
    const { id, roleid, mail_box_type } = params;
    const r = await findMail(id, roleid, mail_box_type);
    if (r && r.id) {
        const starCol = params.mail_box_type === "inbox" ? "starTime" : "senderStarTime";
        const ret = await models_1.NoteMailModel.updateById(r.id, { [starCol]: 0 });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        return data;
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.NoteMailErrors.MailIdNotExist);
    }
}
/** 发送便利贴邮件 (只支持游戏服务器通过ip白名单访问) */
async function noteMailSend(params) {
    const { receiver } = params;
    const now = Date.now();
    const addProps = (0, util_1.pickBy)(params, ["sendTime", "sender", "receiver", "receiverName", "content", "signature", "style"]);
    const props = Object.assign({}, addProps, {
        starTime: 0,
        senderStarTime: 0,
        status: 0 /* Statues.Normal */,
        senderStatus: 0 /* Statues.Normal */,
        createTime: now,
    });
    const id = await models_1.NoteMailModel.insert(props);
    const data = { id };
    (0, service_1.refreshNewNoteMailCache)(receiver);
    return data;
}
/** 清理玩家的便利贴信息，包括收和发, 比如藏宝阁交易时 (CallByGameServer) */
async function noteMailCleanAll(params) {
    const { roleid } = params;
    const cleanOutBox = await models_1.NoteMailModel.updateByCondition({ sender: roleid }, { 'senderStatus': -1 /* Statues.Deleted */ });
    const cleanInBox = await models_1.NoteMailModel.updateByCondition({ receiver: roleid }, { 'status': -1 /* Statues.Deleted */ });
    logger.info({ roleid, cleanOutBox, cleanInBox }, 'noteMailCleanAll');
    const isOk = cleanOutBox.affectedRows >= 0 && cleanInBox.affectedRows >= 0;
    const data = { isOk };
    return data;
}
//# sourceMappingURL=operation.js.map
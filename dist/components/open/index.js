"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/open/competition/list",
        paramsSchema: type_1.ReqSchemas.OpenCompetitionList,
        operation: operation_1.openCompetitionList,
    },
    {
        method: "get",
        url: "/open/competition/options",
        operation: operation_1.openCompetitionOptions,
    },
];
exports.OpenComponent = {
    paths: exports.paths,
    prefix: "/open",
};
//# sourceMappingURL=index.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompetitionAvatarTable = void 0;
exports.openCompetitionList = openCompetitionList;
exports.openCompetitionOptions = openCompetitionOptions;
const _ = require("lodash");
const errorCodes_1 = require("../../errorCodes");
const Competition_1 = require("../../models/Competition");
const util_1 = require("../../common/util");
async function openCompetitionList(params) {
    const { competitionId, serverType, whichTimes } = params;
    let record = await Competition_1.CompetitionV2Model.findOne({
        comptitionId: competitionId,
        serverType: serverType,
        whichTimes: whichTimes
    });
    if (!record) {
        throw errorCodes_1.openErrors.CompetitionNotFound;
    }
    let res = {
        competitionId: record.comptitionId,
        serverType: record.serverType,
        whichTimes: record.whichTimes,
    };
    let careerMap = (0, util_1.keyToRecordMapByKeys)(exports.CompetitionAvatarTable, ['jobId', 'gender']);
    if (record.result) {
        let result = (0, util_1.getJsonInfo)(record.result, []);
        if (!_.isEmpty(result)) {
            for (let item of result) {
                let roleList = item.roleList.map(role => {
                    return {
                        roleName: role.roleName,
                        roleType: role.roleType,
                        avatarUrl: careerMap.get(`${role.career}_${role.gender}`) ? careerMap.get(`${role.career}_${role.gender}`).url : "",
                        career: role.career,
                        gender: role.gender
                    };
                });
                item.roleList = _.sortBy(roleList, 'roleType');
            }
        }
        res['result'] = result;
    }
    if (record.table) {
        let table = (0, util_1.getJsonInfo)(record.table, []);
        table = table.map(item => {
            return {
                rank: item.rank,
                serverName: item.serverName,
                guildName: item.guildName,
                guildMaster: item.guildMaster,
            };
        });
        res['table'] = table;
    }
    return res;
}
const severNameMap = new Map([
    [1, '正式服'],
    [2, '怀旧服'],
    [3, '黄金服'],
]);
const competitionNameMap = new Map([
    [1, '天下第一'],
    [2, '诸神之战'],
    [3, '皇城之巅'],
    [4, '剑试苍穹'],
    [5, '跨服联赛'],
]);
function convertToUppercaseChinese(num) {
    // 处理负数
    if (num < 0) {
        return '负' + convertToUppercaseChinese(Math.abs(num));
    }
    // 处理小数
    if (num !== Math.floor(num)) {
        return '请输入整数';
    }
    const units = ["", "十", "百", "千", "万", "亿", "兆"];
    const digits = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    if (num === 0)
        return digits[0];
    let result = "";
    let unitIndex = 0;
    let zeroFlag = false;
    while (num > 0) {
        const digit = num % 10;
        if (digit > 0) {
            result = digits[digit] + units[unitIndex] + result;
            zeroFlag = false;
        }
        else if (!zeroFlag) {
            result = digits[0] + result;
            zeroFlag = true;
        }
        num = Math.floor(num / 10);
        unitIndex++;
    }
    // 处理特殊情况
    result = result.replace(/零+$/, ''); // 去掉末尾的零
    result = result.replace(/零+/g, '零'); // 将连续的零替换为单个零
    // 处理"十"开头的特殊情况
    if (result.startsWith(digits[1] + units[1])) {
        result = result.slice(1);
    }
    return result;
}
async function openCompetitionOptions() {
    let record = await Competition_1.CompetitionV2Model.find({});
    let res = [];
    let serverMap = new Map();
    for (let item of record) {
        let serverId = item.serverType;
        let whichTimes = item.whichTimes;
        let competitionId = item.comptitionId;
        let server = serverMap.get(serverId);
        if (!server) {
            server = new Map();
            serverMap.set(serverId, server);
        }
        let competition = server.get(competitionId);
        if (!competition) {
            competition = [];
            server.set(competitionId, competition);
        }
        competition.push(whichTimes);
    }
    for (let [serverId, server] of serverMap.entries()) {
        let tmp = {
            serverType: serverId,
            serverTypeName: severNameMap.get(serverId) || "",
            competitionIds: [],
        };
        for (let [competitionId, whichTimesList] of server.entries()) {
            whichTimesList = _.sortBy(whichTimesList, (a) => a);
            let whichTimes = whichTimesList.map(item => {
                return {
                    whichTimes: item,
                    whichTimesName: `第${convertToUppercaseChinese(item)}届`,
                };
            });
            let competition = {
                competitionId: competitionId,
                competitionName: competitionNameMap.get(competitionId) || "",
                whichTimes: whichTimes,
            };
            tmp.competitionIds.push(competition);
        }
        tmp.competitionIds = _.sortBy(tmp.competitionIds, 'competitionId');
        res.push(tmp);
    }
    res = _.sortBy(res, 'serverType');
    return {
        list: res,
    };
}
exports.CompetitionAvatarTable = [
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/1_0.png', jobId: 1, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/1_1.png', jobId: 1, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/2_0.png', jobId: 2, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/2_1.png', jobId: 2, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/3_0.png', jobId: 3, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/3_1.png', jobId: 3, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/4_0.png', jobId: 4, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/4_1.png', jobId: 4, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/5_0.png', jobId: 5, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/5_1.png', jobId: 5, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/6_0.png', jobId: 6, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/6_1.png', jobId: 6, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/7_0.png', jobId: 7, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/7_1.png', jobId: 7, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/8_0.png', jobId: 8, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/8_1.png', jobId: 8, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/9_0.png', jobId: 9, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/9_1.png', jobId: 9, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/11_0.png', jobId: 11, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/11_1.png', jobId: 11, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/12_0.png', jobId: 12, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/12_1.png', jobId: 12, gender: 1 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/13_0.png', jobId: 13, gender: 0 },
    { url: 'http://hi-163-nsh.nosdn.127.net/asserts/competition/avatar/13_1.png', jobId: 13, gender: 1 },
];
//# sourceMappingURL=operation.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextAuditAction = void 0;
exports.textCheck = textCheck;
exports.textCheckSimple = textCheckSimple;
const crypto = require("crypto");
const _ = require("lodash");
const logger_1 = require("../../logger");
const request_1 = require("../../common/request");
const constants_1 = require("../../common/constants");
const config_1 = require("../../common/config");
const logger = (0, logger_1.clazzLogger)("audit/neDun");
function noncer() {
    return _.random(0, 100000000);
}
function genSignature(secretKey, params) {
    const sortKeys = _.sortBy(Object.keys(params));
    const needSignatureStr = _.flatMap(sortKeys, (key) => [key, params[key]]).join("") + secretKey;
    return crypto.createHash("md5").update(needSignatureStr, "utf8").digest("hex");
}
var TextAuditAction;
(function (TextAuditAction) {
    TextAuditAction[TextAuditAction["Pass"] = 0] = "Pass";
    TextAuditAction[TextAuditAction["Suspicion"] = 1] = "Suspicion";
    TextAuditAction[TextAuditAction["Reject"] = 2] = "Reject";
})(TextAuditAction || (exports.TextAuditAction = TextAuditAction = {}));
/**
 * TextCheck
 * @description: https://support.dun.163.com/documents/**********?docId=424375611814748160
 */
async function textCheck(params) {
    if (!config_1.neDunTextCheckCfg.enable) {
        return {
            code: 200,
            result: {
                antispam: { taskId: "fake_task_id", action: TextAuditAction.Pass },
            },
        };
    }
    const now = new Date().getTime();
    const payload = {
        account: params.account,
        secretId: config_1.neDunTextCheckCfg.secretId,
        businessId: config_1.neDunTextCheckCfg.businessId,
        version: config_1.neDunTextCheckCfg.version,
        timestamp: now,
        nonce: noncer(),
        dataId: params.dataId,
        content: params.content,
        title: params.title || "",
    };
    const signature = genSignature(config_1.neDunTextCheckCfg.secretKey, payload);
    const body = { ...payload, signature };
    const ret = await (0, request_1.request)({
        method: "POST",
        url: config_1.neDunTextCheckCfg.apiUrl,
        formData: body,
        json: true,
    });
    return ret;
}
async function textCheckSimple(params) {
    const ret = await textCheck(params);
    if (ret.code === 200) {
        const action = ret.result.antispam.action;
        if (action === TextAuditAction.Pass) {
            return constants_1.AuditStatues.Pass;
        }
        else if (action === TextAuditAction.Suspicion) {
            return config_1.neDunTextCheckCfg.suspicionAsPass ? constants_1.AuditStatues.Pass : constants_1.AuditStatues.Reject;
        }
        else if (action === TextAuditAction.Reject) {
            return constants_1.AuditStatues.Reject;
        }
        else {
            logger.warn({ ret, params }, "NeDunTextCheckActionUnknown");
            return config_1.neDunTextCheckCfg.apiErrorAsPass ? constants_1.AuditStatues.Pass : constants_1.AuditStatues.Auditing;
        }
    }
    else {
        logger.warn({ ret, params }, "NeDunTextCheckApiFail");
        return config_1.neDunTextCheckCfg.apiErrorAsPass ? constants_1.AuditStatues.Pass : constants_1.AuditStatues.Auditing;
    }
}
//# sourceMappingURL=neDun.js.map
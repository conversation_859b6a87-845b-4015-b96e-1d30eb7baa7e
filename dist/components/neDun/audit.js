"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.textCheckFilter = textCheckFilter;
const util_1 = require("../../common/util");
const neDun_1 = require("./neDun");
const constants_1 = require("../../common/constants");
const errorCodes_1 = require("../../errorCodes");
async function textCheckFilter(params) {
    const dataId = params.dataId || (0, util_1.hexMd5)(params.content);
    let account = "";
    if (params.userId) {
        account = "" + params.userId;
    }
    const ret = await (0, neDun_1.textCheckSimple)({
        account,
        dataId,
        content: params.content,
        title: params.title,
    });
    if (ret === constants_1.AuditStatues.Reject) {
        throw errorCodes_1.errorCode.ContainSensitive;
    }
    else {
        return dataId;
    }
}
//# sourceMappingURL=audit.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemeCache = void 0;
const config_1 = require("../../common/config");
const util_1 = require("../../common/util");
const cacheUtil_1 = require("../../services/cacheUtil");
const model_1 = require("./model");
class MemeCacheClass extends cacheUtil_1.GenericCache {
    getKey(roleId) {
        return (0, util_1.cacheKeyGen)("meme_list", { roleId: roleId });
    }
    getExpireTime() {
        return config_1.memeCfg.cacheTime;
    }
    async fetchDataSource(roleId) {
        const rows = await model_1.MemeModel.powerQuery({
            initQuery: model_1.MemeModel.scope().where({ roleId: roleId, status: 0 /* Statues.Normal */ }),
            pagination: { page: 1, pageSize: config_1.memeCfg.maxSizePerRoleId },
            select: model_1.MemeCols,
            orderBy: [["id"], ["desc"]],
        });
        const list = rows.map((item) => {
            return {
                id: item.id,
                auditStatus: item.auditStatus,
                clientId: item.clientId,
                url: item.url,
                createTime: item.createTime,
            };
        });
        return list;
    }
}
exports.MemeCache = new MemeCacheClass();
//# sourceMappingURL=cache.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    MemeList: { type: "object", properties: { roleid: { type: "integer" } }, required: ["roleid"] },
    MemeAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            url: { type: "string" },
            clientId: { type: "integer", minimum: 0 },
        },
        required: ["roleid", "url", "clientId"],
    },
    MemeDel: {
        type: "object",
        properties: { roleid: { type: "integer" }, clientId: { type: "integer" } },
        required: ["roleid", "clientId"],
    },
};
//# sourceMappingURL=type.js.map
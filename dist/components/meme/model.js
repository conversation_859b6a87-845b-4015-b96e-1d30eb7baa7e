"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemeNosToFpModel = exports.MemeNosToFpCols = exports.MemeModel = exports.MemeCols = void 0;
const constants_1 = require("../../common/constants");
const BaseModel2_1 = require("../../models/BaseModel2");
exports.MemeCols = ["id", "roleId", "url", "clientId", "status", "auditStatus", "createTime"];
class MemeModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_meme");
    }
    async getAuditStatusByUrl(url) {
        const query = this.normalScope().where({ url: url }).orderBy("id", "desc").limit(1);
        const r = await this.findOneByQuery(query, ["id", "auditStatus"]);
        if (r && r.id > 0) {
            return r.auditStatus;
        }
        else {
            return constants_1.AuditStatues.Auditing;
        }
    }
    async getMemeCountByRoleId(roleId) {
        const r = await this.count({ roleId: roleId, status: 0 /* Statues.Normal */ });
        return r;
    }
}
exports.MemeModel = new MemeModelClass();
exports.MemeNosToFpCols = ["id", "nosUrl", "fpUrl", "createTime"];
class MemeNosToFpModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_meme_nos_to_fp");
    }
}
exports.MemeNosToFpModel = new MemeNosToFpModelClass();
//# sourceMappingURL=model.js.map
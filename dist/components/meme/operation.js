"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.memeList = memeList;
exports.memeAdd = memeAdd;
exports.memeDel = memeDel;
const config_1 = require("../../common/config");
const constants_1 = require("../../common/constants");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const imageAudit_1 = require("../../services/imageAudit");
const sendImageAudit_1 = require("../../services/sendImageAudit");
const cache_1 = require("./cache");
const model_1 = require("./model");
const logger = (0, logger_1.clazzLogger)("meme.operation");
/** 自定义表情包列表 */
async function memeList(params) {
    const list = await cache_1.MemeCache.get(params.roleid);
    const data = {
        list: list,
    };
    return data;
}
/** 添加到自定义表情 */
async function memeAdd(params) {
    if (!(0, util_1.isValidPhotoUrl)(params.url)) {
        return (0, errorCodes_1.BussError)(errorCodes_1.errorCode.PhotoUrlNotValid);
    }
    const memeCnt = await model_1.MemeModel.getMemeCountByRoleId(params.roleid);
    if (memeCnt >= config_1.memeCfg.maxSizePerRoleId) {
        return (0, errorCodes_1.BussError)(errorCodes_1.errorCode.MemeExceedLimit);
    }
    const auditStatus = await model_1.MemeModel.getAuditStatusByUrl(params.url);
    const addProps = {
        roleId: params.roleid,
        url: params.url,
        status: 0,
        clientId: params.clientId,
        auditStatus: auditStatus,
        createTime: Date.now(),
    };
    const r = await model_1.MemeModel.findOne({ clientId: params.clientId, roleId: params.roleid }, ["id"]);
    const id = 0;
    const data = {
        id: id,
        auditStatus: addProps.auditStatus,
        createTime: addProps.createTime,
    };
    if (r && r.id) {
        data.id = r.id;
        await model_1.MemeModel.updateById(r.id, addProps);
        logger.info({ meme: addProps, id: r.id }, "MemeAddUpdateOk");
    }
    else {
        const newId = await model_1.MemeModel.insert(addProps);
        data.id = newId;
        logger.info({ meme: addProps, id: newId }, "MemeAddInsertOk");
    }
    await cache_1.MemeCache.del(params.roleid);
    if (auditStatus === constants_1.AuditStatues.Auditing) {
        if ((0, util_1.isFpUrl)(params.url)) {
            logger.debug({ meme: addProps }, "MemeFpFileSkipSendPic");
        }
        else {
            const picId = (0, imageAudit_1.genPicIdFromInfo)({ id: "" + data.id, type: "meme" });
            (0, sendImageAudit_1.sendPic2)(config_1.memeCfg.product, config_1.memeCfg.sendPicUrl, [params.url], {
                roleId: params.roleid,
                ip: params.ip,
                picId,
                media: constants_1.EPicMediaType.Image,
            });
        }
    }
    else {
        logger.debug({ meme: addProps }, "MemeAuditedSkipSendPic");
    }
    return data;
}
/** 删除自定义表情 */
async function memeDel(params) {
    const r = await model_1.MemeModel.findOne({ clientId: params.clientId, roleId: params.roleid, status: 0 /* Statues.Normal */ }, [
        "id",
    ]);
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.errorCode.MemeNotFound);
    }
    const ret = await model_1.MemeModel.softDeleteByCondition({ id: r.id });
    logger.info({ meme: r }, "MemeDelOk");
    const data = {
        isOk: ret.affectedRows > 0,
    };
    await cache_1.MemeCache.del(params.roleid);
    return data;
}
//# sourceMappingURL=operation.js.map
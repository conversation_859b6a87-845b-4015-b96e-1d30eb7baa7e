"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemeComponent = exports.paths = void 0;
const injectIpHelper_1 = require("../../middlewares/injectIpHelper");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/meme/list",
        paramsSchema: type_1.ReqSchemas.MemeList,
        operation: operation_1.memeList,
    },
    {
        method: "post",
        url: "/meme/add",
        paramsSchema: type_1.ReqSchemas.MemeAdd,
        before: (0, injectIpHelper_1.injectIpHelper)(),
        operation: operation_1.memeAdd,
    },
    {
        method: "post",
        url: "/meme/del",
        paramsSchema: type_1.ReqSchemas.MemeDel,
        operation: operation_1.memeDel,
    },
];
exports.MemeComponent = {
    paths: exports.paths,
    prefix: "/meme/",
};
//# sourceMappingURL=index.js.map
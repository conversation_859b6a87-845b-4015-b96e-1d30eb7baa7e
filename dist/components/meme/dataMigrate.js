"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFpClient = getFpClient;
exports.memeMigrateFromNosToFp = memeMigrateFromNosToFp;
exports.downloadFile = downloadFile;
const filepicker_1 = require("@leihuo/filepicker");
const util_1 = require("../../common/util");
const logger_1 = require("../../logger");
const model_1 = require("./model");
const config_1 = require("../../common/config");
const fs = require("fs");
const http = require("http");
const Bluebird = require("bluebird");
let fpClient;
function getFpClient() {
    if (!fpClient) {
        const fpCfg = config_1.memeCfg.fpCfg;
        fpClient = filepicker_1.FPClient.create({
            project: fpCfg.project,
            secretKey: fpCfg.secretKey,
            devMode: fpCfg.devMode,
            region: "MainLand",
        });
    }
    return fpClient;
}
const logger = (0, logger_1.clazzLogger)("meme.dataMigrate");
async function getMemeListByMinId(minId, size) {
    const query = model_1.MemeModel.normalScope().where("id", ">", minId).orderBy("id", "asc").limit(size);
    const list = await model_1.MemeModel.executeByQuery(query);
    return list;
}
async function getLastScanId() {
    // 这里是因为玩家的数据可能改了，上次迁移的会找不到对应的表情位置, 去除20个
    const query = model_1.MemeNosToFpModel.scope().orderBy("id", "desc").select(["nosUrl"]).limit(20);
    const rows = await model_1.MemeNosToFpModel.executeByQuery(query);
    if (rows && rows.length > 0) {
        const urls = rows.map((r) => r.nosUrl);
        const memes = await model_1.MemeModel.find({ url: urls });
        if (memes && memes.length > 0) {
            return memes[0].id;
        }
        else {
            return 0;
        }
    }
    return 0;
}
async function memeMigrateFromNosToFp() {
    logger.info("memeMigrateFromNosToFpBegin");
    let scanId = await getLastScanId();
    let memes = await getMemeListByMinId(scanId, 100);
    while (memes && memes.length > 0) {
        for (const meme of memes) {
            const url = meme.url;
            if ((0, util_1.isNosUrl)(url)) {
                try {
                    await processMigrateMeme(meme.id, "" + meme.roleId, url);
                }
                catch (err) {
                    logger.warn({ meme, url }, "memeMigrateFromNosToFpError");
                }
            }
            else {
                logger.debug({ meme }, "memeMigrateFromNosToFpSkip");
            }
            await Bluebird.delay(50);
        }
        scanId = memes[memes.length - 1].id;
        memes = await getMemeListByMinId(scanId, 100);
    }
    logger.info("memeMigrateFromNosToFpEnd");
}
function downloadFile(url, dest) {
    const file = fs.createWriteStream(dest);
    return new Promise((resolve, reject) => {
        http
            .get(url, function (res) {
            res.pipe(file);
            file.on("finish", function () {
                file.close(function (err) {
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve(res);
                    }
                });
            });
        })
            .on("error", function (err) {
            if (err) {
                reject(err);
            }
        });
    });
}
async function processMigrateMeme(memeId, roleId, nosUrl) {
    // step0: check nosUrl has been migrated
    const curMigrate = await model_1.MemeNosToFpModel.findOne({ nosUrl: nosUrl });
    let fpUrl = "";
    if (curMigrate && curMigrate.fpUrl) {
        fpUrl = curMigrate.fpUrl;
    }
    else {
        // step1: download file from nos
        const fileKey = encodeURIComponent(nosUrl);
        const saveNosFilePath = `/tmp/${fileKey}`;
        try {
            const res = await downloadFile(nosUrl, saveNosFilePath);
            if (!res || res.statusCode !== 200) {
                logger.warn({ memeId, roleId, url: nosUrl }, "step1: download nos file failed");
                return;
            }
            else {
                logger.info({ memeId, roleId, url: nosUrl, saveNosFilePath }, "step1: download nos file ok");
            }
        }
        catch (err) {
            logger.error({ memeId, roleId, url: nosUrl, err }, "step1: download nos file failed");
        }
        // step2: upload to fp
        const fpClient = getFpClient();
        try {
            const upRet = await fpClient.uploadFile(saveNosFilePath, {
                uid: roleId,
            });
            fpUrl = upRet.url;
            logger.info({ memeId, roleId, url: nosUrl, fpUrl }, "step2: upload to fp ok");
        }
        catch (err) {
            logger.warn({ memeId, roleId, url: nosUrl, err }, "step2: upload to fp failed");
            return;
        }
        if (!fpUrl) {
            logger.warn({ memeId, roleId, url: nosUrl, fpUrl }, "step2: upload to fp failed");
            return;
        }
        // step3: add migrate record
        const addOps = {
            nosUrl: nosUrl,
            fpUrl: fpUrl,
            createTime: Date.now(),
        };
        try {
            const upRet = await model_1.MemeNosToFpModel.createOrUpdate(addOps);
            logger.info({ memeId, roleId, url: nosUrl, fpUrl, upRet }, "step3: add migrate record ok");
        }
        catch (err) {
            logger.error({ memeId, roleId, url: nosUrl, fpUrl, err }, "step3: add migrate record failed");
        }
    }
    // step4 update meme record url
    try {
        const upRet = await model_1.MemeModel.updateByCondition({ id: memeId, url: nosUrl }, { url: fpUrl });
        if (upRet.affectedRows > 0) {
            logger.info({ memeId, roleId, url: nosUrl, fpUrl, upRet }, "step4: update meme record url ok");
        }
        else {
            logger.warn({ memeId, roleId, url: nosUrl, fpUrl, upRet }, "step4: update meme record url no match");
        }
    }
    catch (err) {
        logger.error({ memeId, roleId, url: nosUrl, fpUrl, err }, "step4: update meme record url failed");
    }
}
//# sourceMappingURL=dataMigrate.js.map
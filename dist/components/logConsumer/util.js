"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseRawGameLogMessage = exports.parseLogRawMessage = exports.toBunyanLogLevel = exports.GAME_LOG_ITEM_REGEX = exports.LOG_ITEM_REGEX = void 0;
const kafkajs_1 = require("kafkajs");
exports.LOG_ITEM_REGEX = /\[(?<eventName>\S+)\]\s*=\s*(?<message>\{.*)/;
exports.GAME_LOG_ITEM_REGEX = /\[(?<eventTime>\S+\s+\S+\s+\S+)\]\[(?<eventName>\S+)\],\s*(?<message>\{.*})/;
function toBunyanLogLevel(level) {
    switch (level) {
        case kafkajs_1.logLevel.ERROR:
            return 'error';
        case kafkajs_1.logLevel.NOTHING:
            return 'trace';
        case kafkajs_1.logLevel.WARN:
            return 'warn';
        case kafkajs_1.logLevel.INFO:
            return 'info';
        case kafkajs_1.logLevel.DEBUG:
            return 'debug';
    }
}
exports.toBunyanLogLevel = toBunyanLogLevel;
function parseLogRawMessage(message) {
    const match = message.match(exports.LOG_ITEM_REGEX);
    if (match && match.groups) {
        const item = {
            eventName: match.groups.eventName,
            message: match.groups.message || '',
        };
        return item;
    }
    else {
        return null;
    }
}
exports.parseLogRawMessage = parseLogRawMessage;
function parseRawGameLogMessage(message) {
    const match = message.match(exports.GAME_LOG_ITEM_REGEX);
    if (match && match.groups) {
        const item = {
            eventTime: new Date(match.groups.eventTime),
            eventName: match.groups.eventName,
            message: match.groups.message || '',
        };
        return item;
    }
    else {
        return null;
    }
}
exports.parseRawGameLogMessage = parseRawGameLogMessage;
//# sourceMappingURL=util.js.map
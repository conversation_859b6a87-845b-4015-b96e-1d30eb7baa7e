"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmServerListComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/gm/server_list/merge_server/list",
        paramsSchema: type_1.ReqSchemas.GmServerListMergeServerList,
        operation: operation_1.gmServerListMergeServerList,
    },
    {
        method: "post",
        url: "/gm/server_list/merge_server/add",
        paramsSchema: type_1.ReqSchemas.GmServerListMergeServerAdd,
        operation: operation_1.gmServerListMergeServerAdd,
    },
    {
        method: "post",
        url: "/gm/server_list/merge_server/clean",
        paramsSchema: type_1.ReqSchemas.GmServerListMergeServerClean,
        operation: operation_1.gmServerListMergeServerClean,
    },
];
exports.GmServerListComponent = {
    paths: exports.paths,
    prefix: "/gm/server_list/merge_server/",
};
//# sourceMappingURL=index.js.map
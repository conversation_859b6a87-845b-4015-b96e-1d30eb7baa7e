"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmServerListMergeServerList = gmServerListMergeServerList;
exports.gmServerListMergeServerAdd = gmServerListMergeServerAdd;
exports.gmServerListMergeServerClean = gmServerListMergeServerClean;
const _ = require("lodash");
const service_1 = require("./service");
/** 查看设置的合服关系列表 */
async function gmServerListMergeServerList() {
    const hash = await (0, service_1.getMergeServerHash)();
    const data = Object.keys(hash).map((k) => {
        const root = +k;
        const from_list = _.pull(hash[k], root);
        return { to: root, from_list };
    });
    return data;
}
/** 添加合服关系 */
async function gmServerListMergeServerAdd(params) {
    const { from_list, to } = params;
    const hash = await (0, service_1.addMergeServer)({ from_list, to });
    const ret = await (0, service_1.saveMergeServerList)(hash);
    const isOk = ret > 0;
    const data = { isOk };
    return data;
}
/** 清理所有设置的合服关系 */
async function gmServerListMergeServerClean() {
    const ret = await (0, service_1.cleanMergeServerList)();
    const isOk = ret > 0;
    const data = { isOk };
    return data;
}
//# sourceMappingURL=operation.js.map
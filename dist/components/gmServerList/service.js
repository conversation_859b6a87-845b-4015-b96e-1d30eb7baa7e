"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMergeServerHash = getMergeServerHash;
exports.saveMergeServerList = saveMergeServerList;
exports.addMergeServer = addMergeServer;
exports.cleanMergeServerList = cleanMergeServerList;
exports.getMergeServerIds = getMergeServerIds;
const _ = require("lodash");
const redis_1 = require("../../common/redis");
const util_1 = require("../../common/util");
const mergeServerListKey = "gm:server_list:merge_servers";
async function getMergeServerHash() {
    const info = await (0, redis_1.getRedis)().getAsync(mergeServerListKey);
    if (info) {
        return (0, util_1.getJsonInfo)(info, {});
    }
    else {
        return {};
    }
}
async function saveMergeServerList(hash) {
    const data = JSON.stringify(hash);
    const ret = await (0, redis_1.getRedis)().setAsync(mergeServerListKey, data);
    return ret.length;
}
async function addMergeServer(e) {
    const hash = await getMergeServerHash();
    const childIds = _.uniq(_.concat(e.from_list, e.to));
    if (hash[e.to]) {
        hash[e.to] = _.uniq(_.concat(hash[e.to], childIds));
    }
    else {
        hash[e.to] = childIds;
    }
    return hash;
}
async function cleanMergeServerList() {
    const ret = await (0, redis_1.getRedis)().delAsync(mergeServerListKey);
    return ret;
}
async function getMergeServerIds(serverId) {
    const hash = await getMergeServerHash();
    const keys = Object.keys(hash);
    for (let rootServerIdStr of keys) {
        const root = parseInt(rootServerIdStr, 10);
        const childIds = hash[root];
        if (root === serverId || _.includes(childIds, serverId)) {
            return { root, serverIds: hash[rootServerIdStr] };
        }
    }
    return { root: serverId, serverIds: [serverId] };
}
//# sourceMappingURL=service.js.map
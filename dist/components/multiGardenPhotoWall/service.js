"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.INIT_HOT_STATE = void 0;
exports.formatAtRoleIds = formatAtRoleIds;
exports.formatHotState = formatHotState;
exports.isLikePhoto = isLikePhoto;
exports.isPickedPhoto = isPickedPhoto;
exports.findPhoto = findPhoto;
exports.findComment = findComment;
exports.findWall = findWall;
exports.findHandPickWall = findHandPickWall;
exports.getLikedPhotoSet = getLikedPhotoSet;
exports.getPickedPhotoSet = getPickedPhotoSet;
exports.getRealWallId = getRealWallId;
exports.getRoleNameFetcher = getRoleNameFetcher;
exports.getPhotoUrlMap = getPhotoUrlMap;
exports.getPhotoUrlFetcher = getPhotoUrlFetcher;
exports.getNewHotState = getNewHotState;
exports.getHotFromState = getHotFromState;
exports.updatePhotoHotState = updatePhotoHotState;
exports.updateCommentLikeCount = updateCommentLikeCount;
exports.addNotificationByAction = addNotificationByAction;
exports.buildNotificationByAction = buildNotificationByAction;
exports.addNotifications = addNotifications;
exports.getMultiGardenPhotoWallNotificationNumInner = getMultiGardenPhotoWallNotificationNumInner;
exports.getRoleInfoFetcher = getRoleInfoFetcher;
exports.auditMultiGardenPhotoWallPhoto = auditMultiGardenPhotoWallPhoto;
exports.getPhotoUrl = getPhotoUrl;
exports.getAuditStatus = getAuditStatus;
exports.getNormalWallUpperBound = getNormalWallUpperBound;
const _ = require("lodash");
const constants_1 = require("../../common/constants");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const roleInfo_1 = require("../../services/roleInfo");
const constant_1 = require("./constant");
const logger = (0, logger_1.clazzLogger)("multiGardenPhotoWall/service");
const ProfileService = require("../../services/profile");
const imageAudit_1 = require("../../services/imageAudit");
const sendImageAudit_1 = require("../../services/sendImageAudit");
const redDot_1 = require("../../services/redDot");
const config_1 = require("../../common/config");
function formatAtRoleIds(r) {
    const roleIds = r.atRoleIds || "";
    return (0, util_1.csvStrToIntArray)(roleIds);
}
function formatHotState(r) {
    const hotStatObj = (0, util_1.getJsonInfo)(r.hotState, {});
    if (hotStatObj) {
        return { like: hotStatObj.like || 0, comment: hotStatObj.comment || 0 };
    }
    else {
        return { like: 0, comment: 0 };
    }
}
exports.INIT_HOT_STATE = { like: 0, comment: 0 };
async function isLikePhoto(roleId, photoId) {
    const r = await models_1.MultiGardenPhotoWallPhotoLikeModel.findOne({ roleId, photoId, status: 0 /* Statues.Normal */ }, ["id"]);
    return !!(r && r.id);
}
async function isPickedPhoto(photoId) {
    const r = await models_1.MultiGardenPhotoWallHandpickModel.findOne({ photoId }, ["id"]);
    return !!(r && r.id);
}
async function findPhoto(id) {
    const r = await models_1.MultiGardenPhotoWallPhotoModel.findOne({ id, status: 0 /* Statues.Normal */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MultiGardenPhotoWallErrors.PhotoNotExist);
    }
    return r;
}
async function findComment(id) {
    const r = await models_1.MultiGardenPhotoWallCommentModel.findOne({ id, status: 0 /* Statues.Normal */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MultiGardenPhotoWallErrors.CommentNotExist);
    }
    return r;
}
async function findWall(id) {
    const r = await models_1.MultiGardenPhotoWallModel.findOne({ id, status: 0 /* Statues.Normal */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MultiGardenPhotoWallErrors.WallNotExist);
    }
    return r;
}
async function findHandPickWall(id) {
    const r = await models_1.MultiGardenPhotoWallModel.findOne({ id, status: 0 /* Statues.Normal */, type: 1 /* EWallType.HandPick */ });
    if (!r) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MultiGardenPhotoWallErrors.HandPickWallNotExist);
    }
    return r;
}
async function getLikedPhotoSet(roleId, photoIds) {
    const rows = await models_1.MultiGardenPhotoWallPhotoLikeModel.find({ status: 0 /* Statues.Normal */, roleId, photoId: photoIds }, { cols: ["id"] });
    const likeIds = rows.map((r) => r.id);
    return new Set(likeIds);
}
async function getPickedPhotoSet(photoIds) {
    const rows = await models_1.MultiGardenPhotoWallHandpickModel.find({ photoId: photoIds }, { cols: ["photoId"] });
    const pickPhotoIds = rows.map((r) => r.photoId);
    return new Set(pickPhotoIds);
}
async function getRealWallId(wallVirtualId, multiGardenId) {
    if (wallVirtualId === constant_1.HandPickWallId) {
        const r = await models_1.MultiGardenPhotoWallModel.findOne({ multiGardenId, type: 1 /* EWallType.HandPick */ }, ["id"]);
        if (r && r.id) {
            return 0;
        }
        else {
            return 0;
        }
    }
    else {
        return _.toInteger(wallVirtualId);
    }
}
async function getRoleNameFetcher(roleIds) {
    const roleNamesMap = await (0, roleInfo_1.getRoleNameMap)(roleIds);
    const getName = (roleId) => (roleNamesMap.has(roleId) ? roleNamesMap.get(roleId) : "");
    return getName;
}
async function getPhotoUrlMap(photoIds) {
    const map = new Map();
    const rows = await models_1.MultiGardenPhotoWallPhotoModel.find({ id: photoIds }, { cols: ["id", "url"] });
    for (let r of rows) {
        map.set(r.id, r.url);
    }
    return map;
}
async function getPhotoUrlFetcher(photoIds) {
    const photoUrlMap = await getPhotoUrlMap(photoIds);
    const getUrl = (id) => (photoUrlMap.has(id) ? photoUrlMap.get(id) : "");
    return getUrl;
}
function getNewHotState(hotState, action) {
    const newState = _.clone(hotState) || { like: 0, comment: 0 };
    if (action === 0 /* EPhotoAction.Like */) {
        newState.like = hotState.like + 1;
    }
    else if (action === 1 /* EPhotoAction.CancelLike */) {
        newState.like = Math.max(0, hotState.like - 1);
    }
    else if (action === 2 /* EPhotoAction.Comment */) {
        newState.comment = hotState.comment + 1;
    }
    else if (action === 3 /* EPhotoAction.DelComment */) {
        newState.comment = Math.max(0, hotState.comment - 1);
    }
    else {
        // do nothing
    }
    return newState;
}
function getHotFromState(hotState) {
    return hotState.like + hotState.comment;
}
async function updatePhotoHotState(photo, action) {
    const hotState = formatHotState(photo);
    const newState = getNewHotState(hotState, action);
    const newHot = getHotFromState(newState);
    const ret = await models_1.MultiGardenPhotoWallPhotoModel.updateById(photo.id, { hotState: JSON.stringify(newState), hot: newHot });
    return ret;
}
async function updateCommentLikeCount(comment, action) {
    const newLikeCount = action === 0 /* ECommentAction.Like */ ? comment.likeCount + 1 : Math.max(0, comment.likeCount - 1);
    const ret = await models_1.MultiGardenPhotoWallCommentModel.updateById(comment.id, { likeCount: newLikeCount });
    return ret;
}
async function addNotificationByAction(type, props) {
    if (props.roleId !== props.targetId) {
        const insertProps = buildNotificationByAction(type, props);
        await models_1.MultiGardenPhotoWallNotificationModel.insert(insertProps);
        await (0, redDot_1.multiGardenPhotoWallRedDotEnable)(props.targetId);
    }
}
function buildNotificationByAction(type, props) {
    const insertProps = {
        roleId: props.roleId,
        type,
        targetId: props.targetId,
        text: props.text || "",
        relateId: props.relateId || 0,
        photoId: props.photoId,
        wallId: props.wallId,
        status: 0 /* Statues.Normal */,
        createTime: Date.now(),
    };
    return insertProps;
}
async function addNotifications(notifications) {
    const realInsert = notifications.filter((r) => r.roleId !== r.targetId);
    if (realInsert && realInsert.length > 0) {
        await models_1.MultiGardenPhotoWallNotificationModel.insertBatch(realInsert);
        for (let item of realInsert) {
            await (0, redDot_1.multiGardenPhotoWallRedDotEnable)(item.targetId);
        }
    }
}
async function getMultiGardenPhotoWallNotificationNumInner(roleId) {
    const cnt = await models_1.MultiGardenPhotoWallNotificationModel.count({ targetId: roleId, status: 0 /* Statues.Normal */ });
    return cnt;
}
async function getRoleInfoFetcher(roleIds, curRoleId) {
    const roleInfoMap = await ProfileService.getRoleInfo(roleIds, curRoleId);
    const getRoleInfo = (roleId) => (roleInfoMap[roleId] ? roleInfoMap[roleId] : null);
    return getRoleInfo;
}
function auditMultiGardenPhotoWallPhoto(roleId, photoUrl, photoId, ip) {
    const picId = (0, imageAudit_1.genPicIdFromInfo)({ type: "multi_garden_photo_wall_photo", id: "" + photoId });
    (0, sendImageAudit_1.sendPic)([photoUrl], {
        roleId: roleId,
        picId: picId,
        media: constants_1.EPicMediaType.Image,
        ip: ip,
    });
    return picId;
}
const AuditImageUrl = "http://hi-163-nsh.nosdn.127.net/assert/images/guild_photo_wall_audit.png";
const AuditImageUrlHighRes = "http://hi-163-nsh.nosdn.127.net/assert/images/guild_image_detail_auditing.png";
function getPhotoUrl(url, auditStatus, isUserSelf, isHighRes = false) {
    if (config_1.testCfg.skip_photo_wall_audit) {
        return url;
    }
    if (auditStatus === constants_1.EAuditStatus.Reject) {
        return "http://hi-163-nsh.nosdn.127.net/assert/images/guild_photo_wall_reject.png";
    }
    else if ((0, util_1.contains)([constants_1.EAuditStatus.Init, constants_1.EAuditStatus.Auditing], auditStatus)) {
        if (isUserSelf) {
            return url;
        }
        else {
            return isHighRes ? AuditImageUrlHighRes : AuditImageUrl;
        }
    }
    else {
        return url;
    }
}
function getAuditStatus(auditStatus) {
    if (config_1.testCfg.skip_photo_wall_audit) {
        return constants_1.EAuditStatus.PASS;
    }
    return auditStatus;
}
function getNormalWallUpperBound(multiGardenLevel) {
    const queryMultiGardenLevel = Math.max(constant_1.MinMultiGardenLevel, Math.min(multiGardenLevel, constant_1.MaxMultiGardenLevel));
    const curLimitRow = constant_1.MultiGardenPhotoWallTable.find(r => r.multiGardenLevel === queryMultiGardenLevel);
    if (curLimitRow) {
        return curLimitRow.wallLimit;
    }
    else {
        logger.warn({ multiGardenLevel, curLimitRow, queryMultiGardenLevel }, 'getNormalWallUpperBoundError');
        return 1;
    }
}
//# sourceMappingURL=service.js.map
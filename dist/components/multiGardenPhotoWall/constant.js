"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CertifiedPlayerMaxNum = exports.MaxHandPickWallSize = exports.OpenHandPickLimit = exports.MaxAtRoleIdSize = exports.MaxPhotosWallPerMultiGarden = exports.MaxMultiGardenLevel = exports.MinMultiGardenLevel = exports.MultiGardenPhotoWallTable = exports.MaxPhotosSizePerWall = exports.HandPickWallId = void 0;
exports.HandPickWallId = "handpick";
exports.MaxPhotosSizePerWall = 50;
exports.MultiGardenPhotoWallTable = [
    { multiGardenLevel: 1, newTemplate: 1, quality: 'low', wallLimit: 1, photoLimit: 8 },
    { multiGardenLevel: 2, newTemplate: 0, quality: 'low', wallLimit: 1, photoLimit: 8 },
    { multiGardenLevel: 3, newTemplate: 1, quality: 'low', wallLimit: 2, photoLimit: 16 },
    { multiGardenLevel: 4, newTemplate: 1, quality: 'low', wallLimit: 3, photoLimit: 24 },
    { multiGardenLevel: 5, newTemplate: 1, quality: 'medium', wallLimit: 3, photoLimit: 30 },
    { multiGardenLevel: 6, newTemplate: 1, quality: 'medium', wallLimit: 3, photoLimit: 30 },
    { multiGardenLevel: 7, newTemplate: 1, quality: 'high', wallLimit: 3, photoLimit: 36 },
];
exports.MinMultiGardenLevel = 1;
exports.MaxMultiGardenLevel = 7;
exports.MaxPhotosWallPerMultiGarden = 3;
exports.MaxAtRoleIdSize = 10;
exports.OpenHandPickLimit = {
    MinMultiGardenLevel: 5,
    MinPhotoCnt: 30
};
exports.MaxHandPickWallSize = 1;
exports.CertifiedPlayerMaxNum = 1000;
//# sourceMappingURL=constant.js.map
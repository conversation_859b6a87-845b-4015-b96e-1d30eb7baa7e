"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiGardenPhotoWallComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/multi_garden/photo_wall/list",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallList,
        operation: operation_1.multiGardenPhotoWallList,
    },
    {
        method: "get",
        url: "/multi_garden/photo_wall/photo/:id/show",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoIdShow,
        operation: operation_1.multiGardenPhotoWallPhotoIdShow,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/photo/:id/del",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoPhotoIdCommentDel,
        operation: operation_1.multiGardenPhotoWallPhotoIdDel,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/photo/:id/move",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoIdMove,
        operation: operation_1.multiGardenPhotoWallPhotoIdMove,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/handpick_wall/up",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallHandpickWallUp,
        operation: operation_1.multiGardenPhotoWallHandpickWallUp,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/handpick_wall/down",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallHandpickWallDown,
        operation: operation_1.multiGardenPhotoWallHandpickWallDown,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/photo/:id/like",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoIdLike,
        operation: operation_1.multiGardenPhotoWallPhotoIdLike,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/photo/:id/cancel_like",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoIdCancelLike,
        operation: operation_1.multiGardenPhotoWallPhotoIdCancelLike,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/comment/:id/like",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallCommentIdLike,
        operation: operation_1.multiGardenPhotoWallCommentIdLike,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/comment/:id/cancel_like",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallCommentIdCancelLike,
        operation: operation_1.multiGardenPhotoWallCommentIdCancelLike,
    },
    {
        method: "get",
        url: "/multi_garden/photo_wall/photo/:id/comment/list",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoIdCommentList,
        operation: operation_1.multiGardenPhotoWallPhotoIdCommentList,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/photo/:id/comment/add",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoIdCommentAdd,
        operation: operation_1.multiGardenPhotoWallPhotoIdCommentAdd,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/photo/:photo_id/comment/del",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallPhotoPhotoIdCommentDel,
        operation: operation_1.multiGardenPhotoWallPhotoIdCommentDel,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/:id/photo/add",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallIdPhotoAdd,
        operation: operation_1.multiGardenPhotoWallIdPhotoAdd,
    },
    {
        method: "get",
        url: "/multi_garden/photo_wall/:id/show",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallIdShow,
        operation: operation_1.multiGardenPhotoWallIdShow,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/:id/update",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallIdUpdate,
        operation: operation_1.multiGardenPhotoWallIdUpdate,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/add",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallAdd,
        operation: operation_1.multiGardenPhotoWallAdd,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/multi_garden/photo_wall/notifications/list",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallNotificationsList,
        operation: operation_1.multiGardenPhotoWallNotificationsList,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/notifications/read",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallNotificationsRead,
        operation: operation_1.multiGardenPhotoWallNotificationsRead,
    },
    {
        method: "post",
        url: "/multi_garden/photo_wall/notifications/read_all",
        paramsSchema: type_1.ReqSchemas.MultiGardenPhotoWallNotificationsReadAll,
        operation: operation_1.multiGardenPhotoWallNotificationsReadAll,
    },
];
exports.MultiGardenPhotoWallComponent = {
    paths: exports.paths,
    prefix: "/multi_garden/photo_wall/",
};
//# sourceMappingURL=index.js.map
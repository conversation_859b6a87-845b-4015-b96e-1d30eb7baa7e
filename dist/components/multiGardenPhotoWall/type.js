"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    MultiGardenPhotoWallList: {
        roleid: { type: Number },
        multiGardenId: { type: String },
    },
    MultiGardenPhotoWallPhotoIdShow: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MultiGardenPhotoWallPhotoIdMove: {
        roleid: { type: Number },
        id: { type: Number },
        slot: { type: Number },
    },
    MultiGardenPhotoWallHandpickWallUp: {
        roleid: { type: Number },
        photoId: { type: Number },
        wallId: { type: Number },
        slot: { type: Number },
    },
    MultiGardenPhotoWallHandpickWallDown: {
        roleid: { type: Number },
        wallId: { type: Number },
        slot: { type: Number },
    },
    MultiGardenPhotoWallPhotoIdDel: {
        roleid: { type: Number },
        isLeader: { type: Boolean },
        id: { type: Number },
    },
    MultiGardenPhotoWallPhotoIdLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MultiGardenPhotoWallPhotoIdCancelLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MultiGardenPhotoWallCommentIdLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MultiGardenPhotoWallCommentIdCancelLike: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MultiGardenPhotoWallPhotoIdCommentList: {
        roleid: { type: Number },
        id: { type: Number },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    MultiGardenPhotoWallPhotoIdCommentAdd: {
        roleid: { type: Number },
        id: { type: Number },
        text: { type: String },
        replyId: { type: Number, required: false },
    },
    MultiGardenPhotoWallPhotoPhotoIdCommentDel: {
        roleid: { type: Number },
        isLeader: { type: Boolean },
        id: { type: Number },
    },
    MultiGardenPhotoWallIdPhotoAdd: {
        roleid: { type: Number },
        id: { type: Number },
        overwrite: { type: Boolean, required: false, default: true },
        text: { type: String, required: false },
        url: { type: String },
        slot: { type: Number },
        atRoleIds: { type: Array, required: false, items: { type: Number } },
    },
    MultiGardenPhotoWallIdShow: {
        roleid: { type: Number },
        id: { type: Number },
    },
    MultiGardenPhotoWallIdUpdate: {
        roleid: { type: Number },
        id: { type: Number },
        name: { type: String, required: false },
        templateId: { type: Number, required: false },
        frameId: { type: Number, required: false },
        type: { type: Number, required: false, values: [0, 1] },
    },
    MultiGardenPhotoWallAdd: {
        roleid: { type: Number, required: false, default: 0 },
        multiGardenId: { type: String },
        multiGardenLevel: { type: Number },
        name: { type: String, required: false },
        templateId: { type: Number, required: false },
        frameId: { type: Number, required: false },
        type: { type: Number, required: false, values: [0, 1] },
    },
    MultiGardenPhotoWallNotificationsList: {
        roleid: { type: Number },
        status: { type: Number, required: false, values: [0, 1] },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    MultiGardenPhotoWallNotificationsRead: {
        roleid: { type: Number },
        notificationId: { type: Number },
    },
    MultiGardenPhotoWallNotificationsReadAll: {
        roleid: { type: Number },
    },
};
//# sourceMappingURL=type.js.map
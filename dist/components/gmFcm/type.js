"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    GmFcmTyAddKickoffTask: {
        type: "object",
        properties: { urs: { type: "string" }, kickDate: { type: "string" } },
        required: ["urs", "kickDate"],
    },
    GmFcmGameidGetDailyOnlineTime: {
        type: "object",
        properties: { gameid: { type: "string" }, urs: { type: "string" } },
        required: ["gameid", "urs"],
    },
    GmFcmGameidSetDailyOnlineTime: {
        type: "object",
        properties: { gameid: { type: "string" }, urs: { type: "string" }, duration: { type: "integer" } },
        required: ["gameid", "urs", "duration"],
    },
};
//# sourceMappingURL=type.js.map
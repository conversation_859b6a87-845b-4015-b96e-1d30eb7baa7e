"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmFcmComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/gm/fcm/ty/add_kickoff_task",
        paramsSchema: type_1.ReqSchemas.GmFcmTyAddKickoffTask,
        operation: operation_1.gmFcmTyAddKickoffTask,
    },
    {
        method: "get",
        url: "/gm/fcm/:gameid/get_daily_online_time",
        paramsSchema: type_1.ReqSchemas.GmFcmGameidGetDailyOnlineTime,
        operation: operation_1.gmFcmGameidGetDailyOnlineTime,
    },
    {
        method: "post",
        url: "/gm/fcm/:gameid/set_daily_online_time",
        paramsSchema: type_1.ReqSchemas.GmFcmGameidSetDailyOnlineTime,
        operation: operation_1.gmFcmGameidSetDailyOnlineTime,
    },
];
exports.GmFcmComponent = {
    paths: exports.paths,
    prefix: "/gm/fcm/ty/",
};
//# sourceMappingURL=index.js.map
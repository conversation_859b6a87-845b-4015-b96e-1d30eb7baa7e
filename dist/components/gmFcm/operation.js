"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmFcmTyAddKickoffTask = gmFcmTyAddKickoffTask;
exports.gmFcmGameidGetDailyOnlineTime = gmFcmGameidGetDailyOnlineTime;
exports.gmFcmGameidSetDailyOnlineTime = gmFcmGameidSetDailyOnlineTime;
const redis_1 = require("../../common/redis");
const kickoffManager_1 = require("../fcm/kickoffManager");
const playerOnlineStatus_1 = require("../fcm/playerOnlineStatus");
/** 添加天谕踢人任务 */
async function gmFcmTyAddKickoffTask(params) {
    const ret = await kickoffManager_1.KickoffQueue.getInstance("d21" /* EGameId.TY */).addKickoffTask(params.urs, new Date(params.kickDate));
    return ret;
}
/** 查询当日在线时长 */
async function gmFcmGameidGetDailyOnlineTime(params) {
    const duration = await playerOnlineStatus_1.PlayerOnlineStatus.create(params.gameid, params.urs).getCurDailyPlayDuration(new Date());
    return { duration };
}
/** 设置当日在线时长 */
async function gmFcmGameidSetDailyOnlineTime(params) {
    const k1 = playerOnlineStatus_1.PlayerOnlineStatus.create(params.gameid, params.urs).getKey();
    const k2 = playerOnlineStatus_1.PlayerOnlineStatus.create(params.gameid, params.urs).getDailyOnlineTimeKey(Date.now());
    const ret1 = await (0, redis_1.getRedis)().delAsync(k1);
    const ret2 = await (0, redis_1.getRedis)().setAsync(k2, params.duration);
    return { ret1, ret2 };
}
//# sourceMappingURL=operation.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverCompetitionAuthCheckByToken = serverCompetitionAuthCheckByToken;
const ajvCheck_1 = require("../../common/ajvCheck");
const config_1 = require("../../common/config");
const httpStatus_1 = require("../../common/httpStatus");
const util_1 = require("../../common/util");
const helper_1 = require("../../helper");
const logger_1 = require("../../logger");
async function serverCompetitionAuthCheckByToken(req, res, next) {
    let schema = {
        properties: {
            time: { type: "number" },
            token: { type: "string" }
        },
        required: ["time", "token"]
    };
    try {
        let params = req.params;
        (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
        let signStr = `${params.time}${config_1.AUTH_TOKEN_SALT}`;
        let expectToken = (0, util_1.hexMd5)(signStr);
        if (expectToken === params.token) {
            next();
        }
        else {
            logger_1.logger.warn("CheckTokenFailed", {
                url: req.url,
                params: params,
                signStr: signStr,
                expectToken: expectToken,
                actualToken: params.token,
                skipToken: config_1.testCfg.skip_token
            });
            if (config_1.testCfg.skip_token) {
                logger_1.logger.warn("SkipTokenCheck");
                next();
            }
            else {
                res.send({ code: httpStatus_1.HTTP_STATUS_CODES.UNAUTHORIZED, message: "Token invalid" });
            }
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=middleware.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerCompetitionComponent = exports.paths = void 0;
const middleware_1 = require("./middleware");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/server_competition/sync_result",
        paramsSchema: type_1.ReqSchemas.ServerCompetitionSyncResult,
        before: middleware_1.serverCompetitionAuthCheckByToken,
        operation: operation_1.serverCompetitionSyncResult,
    },
    {
        method: "post",
        url: "/server_competition/sync_table",
        paramsSchema: type_1.ReqSchemas.ServerCompetitionSyncTable,
        before: middleware_1.serverCompetitionAuthCheckByToken,
        operation: operation_1.serverCompetitionSyncTable,
    },
];
exports.ServerCompetitionComponent = {
    paths: exports.paths,
    prefix: "/server_competition",
};
//# sourceMappingURL=index.js.map
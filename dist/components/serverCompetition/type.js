"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    ServerCompetitionSyncResult: {
        type: "object",
        properties: {
            time: { type: "number" },
            token: { type: "string" },
            data: {
                type: "object",
                properties: {
                    serverType: { type: "number" },
                    competitionId: { type: "number" },
                    whichTimes: { type: "number" },
                    result: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                rank: { type: "number" },
                                server: { type: "number" },
                                teamName: { type: "string" },
                                roleList: {
                                    type: "array",
                                    items: {
                                        type: "object",
                                        properties: {
                                            roleName: { type: "string" },
                                            career: { type: "number" },
                                            gender: { type: "number" },
                                            roleType: { type: "number" },
                                            role: { type: "number" },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        },
    },
    ServerCompetitionSyncTable: {
        type: "object",
        properties: {
            time: { type: "number" },
            token: { type: "string" },
            data: {
                type: "object",
                properties: {
                    serverType: { type: "number" },
                    competitionId: { type: "number" },
                    whichTimes: { type: "number" },
                    table: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                rank: { type: "number" },
                                server: { type: "number" },
                                teamName: { type: "string" },
                                guildName: { type: "string" },
                                guildMaster: { type: "string" },
                            },
                        },
                    },
                },
            },
        },
    },
};
//# sourceMappingURL=type.js.map
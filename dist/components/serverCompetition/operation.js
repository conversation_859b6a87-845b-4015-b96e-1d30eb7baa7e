"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverCompetitionSyncResult = serverCompetitionSyncResult;
exports.serverCompetitionSyncTable = serverCompetitionSyncTable;
const _ = require("lodash");
const Competition_1 = require("../../models/Competition");
async function serverCompetitionSyncResult(params) {
    let data = params.data;
    let now = Date.now();
    let insert = {
        comptitionId: data.competitionId,
        serverType: data.serverType,
        whichTimes: data.whichTimes,
        table: "",
        createTime: now,
        updateTime: now
    };
    if (data.result) {
        insert.result = JSON.stringify(data.result);
    }
    let origin = await Competition_1.CompetitionV2Model.findOne({
        comptitionId: data.competitionId,
        serverType: data.serverType,
        whichTimes: data.whichTimes
    });
    if (origin) {
        await Competition_1.CompetitionV2Model.updateById(origin.id, _.pick(insert, ['result', 'updateTime']));
    }
    else {
        await Competition_1.CompetitionV2Model.insert(insert);
    }
    return {
        isOk: true,
    };
}
async function serverCompetitionSyncTable(params) {
    let data = params.data;
    let now = Date.now();
    let insert = {
        comptitionId: data.competitionId,
        serverType: data.serverType,
        whichTimes: data.whichTimes,
        result: "",
        table: JSON.stringify(data.table),
        createTime: now,
        updateTime: now
    };
    let origin = await Competition_1.CompetitionV2Model.findOne({
        comptitionId: data.competitionId,
        serverType: data.serverType,
        whichTimes: data.whichTimes
    });
    if (origin) {
        await Competition_1.CompetitionV2Model.updateById(origin.id, _.pick(insert, ['table', 'updateTime']));
    }
    else {
        await Competition_1.CompetitionV2Model.insert(insert);
    }
    return {
        isOk: true,
    };
}
//# sourceMappingURL=operation.js.map
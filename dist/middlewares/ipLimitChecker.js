"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ipLimitChecker = ipLimitChecker;
const util_1 = require("../common/util");
const config_1 = require("../common/config");
function ipLimitChecker(ipWhiteList) {
    return function check(req, res, next) {
        if (config_1.testCfg.test_env) {
            next();
        }
        else {
            let ipSet = new Set(ipWhiteList);
            ipSet.add('::ffff:127.0.0.1');
            ipSet.add('127.0.0.1');
            let ip = (0, util_1.getIp)(req);
            if (ipSet.has(ip)) {
                next();
            }
            else {
                res.send({ code: -1, message: 'access ip is not in whiteList', accessIp: ip });
            }
        }
    };
}
//# sourceMappingURL=ipLimitChecker.js.map
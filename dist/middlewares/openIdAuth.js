"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.openIdHandler = void 0;
exports.openIdAuth = openIdAuth;
exports.useOpenIdLoginChecker = useOpenIdLoginChecker;
exports.login = login;
exports.logout = logout;
exports.loginInfo = loginInfo;
const config_1 = require("../common/config");
const helper_1 = require("../helper");
const authCheck_1 = require("./authCheck");
const logger_1 = require("../logger");
const config = require("../common/config");
const leihuo_node_openid_1 = require("@leihuo/leihuo-node-openid");
exports.openIdHandler = leihuo_node_openid_1.OpenIdClass.create({
    //@ts-ignore
    mode: config_1.openIdCfg.mode,
    secret: config_1.openIdCfg.secret, //x2g&^JtBd@l2`1
    infoKey: config_1.openIdCfg.infoKey, //common_auth_corp_info
    tokenKey: config_1.openIdCfg.tokenKey, //common_auth_corp_token
    cookieCfg: config_1.openIdCfg.cookieCfg,
    logger: logger_1.logger
});
async function openIdAuth(req, res, next) {
    if (config_1.testCfg.skip_openId_auth || req.route.skipOpenIdAuth) {
        req.session = { mail: "skip_openId_auth" };
        return next();
    }
    let cookies = req.cookies;
    let ret = exports.openIdHandler.cookieAuth(cookies);
    if (!ret.validate) {
        return res.send({ code: -1, msg: ret.msg });
    }
    else {
        req.session = ret.data;
    }
    next();
}
function openIdPrivChecker(checkOpenIdPermission) {
    return async function (req, res, next) {
        let openId = req.session?.mail;
        try {
            let hasPermission = await checkOpenIdPermission(openId);
            if (hasPermission) {
                next();
            }
            else {
                logger_1.logger.info(`openId no permission: ${openId}`);
                throw { code: -1, msg: '请联系管理员添加权限' };
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    };
}
async function useOpenIdLoginChecker(app, urlPrefix, checkOpenIdPermission) {
    (0, helper_1.preUseWhenUrlPrefix)(app, urlPrefix, authCheck_1.skipSkeyAuthMiddleware);
    (0, helper_1.useWhenUrlPrefix)(app, urlPrefix, openIdAuth);
    if (!config_1.testCfg.skip_openId_auth) {
        (0, helper_1.useWhenUrlPrefix)(app, urlPrefix, openIdPrivChecker(checkOpenIdPermission));
    }
}
async function login(req, res, next) {
    try {
        let schema = {
            redirect: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        exports.openIdHandler.login(req, res, next);
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function logout(req, res, next) {
    clearSession(res);
    res.send({ code: 0, data: true });
}
function clearSession(res) {
    res.clearCookie(config.openIdCfg.infoKey, config.cookieCfg);
    res.clearCookie(config.openIdCfg.tokenKey, config.cookieCfg);
}
async function loginInfo(req, res, next) {
    try {
        let openId = config.testCfg.skip_openId_auth ? "skip_openId_auth" : req.session.mail;
        res.send({ code: 0, data: openId });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=openIdAuth.js.map
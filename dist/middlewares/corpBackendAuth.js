"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorpBackendAuth = void 0;
const errorCodes_1 = require("../errorCodes");
const openIdAuth_1 = require("./openIdAuth");
const OpenIdRoleModel_1 = require("../models/OpenIdRoleModel");
const logger_1 = require("../logger");
const helper_1 = require("../helper");
const logger = (0, logger_1.clazzLogger)("middleware:openIdRoleMiddleware");
class CorpBackendAuth {
    constructor() {
    }
    /**
     * 企业登录认证中间件
     * 检查用户是否已登录,验证cookie中的认证信息
     *
     * @param req Express请求对象
     * @param res Express响应对象
     * @param next Express next函数
     *
     * @returns 如果认证失败,返回未登录错误响应
     *          如果认证成功,设置session并调用next()继续处理
     */
    checkCorpLoginAuthMiddleware(req, res, next) {
        const authRet = openIdAuth_1.openIdHandler.cookieAuth(req.cookies);
        if (!authRet.validate) {
            let bizError = errorCodes_1.BizCommonErrors.NotLogin;
            logger.warn({ req, authRet, cookies: req.cookies }, 'ParseCorpCookieFail');
            res.json({ code: bizError.code, msg: bizError.msg });
            return;
        }
        logger.info({ req, authRet }, 'CheckCorpLoginAuthOK');
        req.session = authRet.data;
        next();
    }
    /**
     * 企业角色权限范围认证中间件
     * 检查用户是否有对应scope的权限
     *
     * @param scope 权限范围ID
     * @returns 中间件函数
     *
     * 中间件功能:
     * 1. 检查session是否存在,不存在则返回未登录错误
     * 2. 根据session中的邮箱和scope查询用户角色权限
     * 3. 如果用户没有对应权限则返回未注册错误
     * 4. 将用户角色信息保存到session中
     */
    checkUserRegisitorMiddleware(scope) {
        async function checkAuth(req, res, next) {
            try {
                const session = req.session;
                if (!session) {
                    await (0, errorCodes_1.BussError)(errorCodes_1.BizCommonErrors.NotLogin);
                }
                const user = await OpenIdRoleModel_1.default.findRoleByOpenIdAndScope(session.mail, scope);
                if (!user) {
                    await (0, errorCodes_1.BussError)(errorCodes_1.BizCommonErrors.NotRegister);
                }
                session.authUser = user;
                next();
            }
            catch (error) {
                (0, helper_1.errorHandler)(error, req, res, next);
            }
        }
        return checkAuth;
    }
    /**
     * 检查用户是否是管理员
     *
     * @returns 中间件函数
     *
     * 中间件功能:
     * 1. 检查session是否存在,不存在则返回未注册错误
     * 2. 检查session中的authUser是否存在,不存在则返回未注册错误
     * 3. 检查authUser的role_type是否是管理员
     * 4. 如果authUser的role_type不是管理员则返回角色类型不是管理员错误
     * 5. 如果authUser的role_type是管理员则继续处理
     */
    checkAuthUserIsAdminMiddleware() {
        async function checkAdmin(req, res, next) {
            try {
                const session = req.session;
                if (!session) {
                    await (0, errorCodes_1.BussError)(errorCodes_1.BizCommonErrors.NotRegister);
                }
                const authUser = session.authUser;
                if (!authUser) {
                    await (0, errorCodes_1.BussError)(errorCodes_1.BizCommonErrors.NotRegister);
                }
                if (authUser.role_type !== OpenIdRoleModel_1.AdminRoleType.ADMIN) {
                    await (0, errorCodes_1.BussError)(errorCodes_1.BizCommonErrors.RoleTypeNotAdmin);
                }
                next();
            }
            catch (error) {
                (0, helper_1.errorHandler)(error, req, res, next);
            }
        }
        return checkAdmin;
    }
}
exports.CorpBackendAuth = CorpBackendAuth;
exports.default = new CorpBackendAuth();
//# sourceMappingURL=corpBackendAuth.js.map
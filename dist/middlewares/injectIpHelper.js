"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.injectIpHelper = injectIpHelper;
const util_1 = require("../common/util");
// help inject ip to params with customize name
function injectIpHelper(name = "ip") {
    return function (req, res, next) {
        const ip = (0, util_1.getIp)(req);
        req.params[name] = ip;
        next();
    };
}
//# sourceMappingURL=injectIpHelper.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loginChecker = loginChecker;
exports.levelCheck = levelCheck;
exports.skipSkeyAuthMiddleware = skipSkeyAuthMiddleware;
exports.skipLevelCheckMiddleware = skipLevelCheckMiddleware;
exports.authCheckByToken = authCheckByToken;
exports.clubWebCookieCheck = clubWebCookieCheck;
const _ = require("lodash");
const auth_1 = require("../common/auth");
const helper_1 = require("../helper");
const profile_1 = require("../services/profile");
const logger_1 = require("../logger");
const config_1 = require("../common/config");
const config_2 = require("../common/config");
const config_all_1 = require("../common/config.all");
const util_1 = require("../common/util");
const httpStatus_1 = require("../common/httpStatus");
async function loginChecker(req, res, next) {
    let route = req.route;
    if (config_2.testCfg.skip_skey_check) {
        levelCheck(req, res, next);
    }
    else if (req.skipSkey || route.skipSkey) {
        next();
    }
    else {
        try {
            let schema = {
                roleid: { type: Number },
                skey: { type: String },
            };
            await (0, helper_1.checkParams)(req.params, schema);
            let result = await auth_1.AuthClass.check({ clubWebRoleId: req.params.roleid, skey: req.params.skey }, "clubWebRoleId");
            if (result.validate) {
                req.session = result.session;
                levelCheck(req, res, next);
            }
            else {
                throw result.error;
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
async function levelCheck(req, res, next) {
    let params = req.params;
    let route = req.route;
    if (!params.roleid || req.skipLevel || route.skipLevel || config_2.testCfg.skip_level) {
        next();
    }
    else {
        try {
            let roleId = params.roleid;
            let level = await (0, profile_1.getPlayerLevel)(roleId);
            if (level >= config_1.mdCfg.openMinLevel) {
                next();
            }
            else {
                res.send({ code: -1, msg: "等级过低，梦岛未开放" });
            }
        }
        catch (err) {
            logger_1.logger.error(err, "levelCheck");
            res.send({ code: -1, msg: "level check error" });
        }
    }
}
function skipSkeyAuthMiddleware(req, res, next) {
    req.skipSkey = true;
    next();
}
function skipLevelCheckMiddleware(req, res, next) {
    req.skipLevel = true;
    next();
}
async function authCheckByToken(req, res, next) {
    let schema = {
        time: { type: Number },
        nonce: { type: String, minlen: 6, maxlen: 6 },
        token: { type: String },
    };
    try {
        let params = req.params;
        await (0, helper_1.checkParams)(req.params, schema);
        let keys = Object.keys(_.omit(params, ["token"])).sort();
        let values = keys.map((k) => params[k]);
        let signStr = [...values, config_all_1.AUTH_TOKEN_SALT].join("");
        let expectToken = (0, util_1.hexMd5)(signStr);
        if (expectToken === params.token) {
            next();
        }
        else {
            logger_1.logger.warn("CheckTokenFailed", {
                url: req.url,
                params: params,
                sortKeys: keys,
                actualToken: params.token,
                expectToken: expectToken,
                signStr: signStr,
                skipToken: config_2.testCfg.skip_token
            });
            if (config_2.testCfg.skip_token) {
                logger_1.logger.warn("SkipTokenCheck");
                next();
            }
            else {
                res.send({ code: httpStatus_1.HTTP_STATUS_CODES.UNAUTHORIZED, message: "Token invalid" });
            }
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function clubWebCookieCheck(req, res, next) {
    try {
        let cookies = req.cookies;
        if (!config_1.clubWebCfg.loginCookieCheck || req.route.skipAuth) {
            return next();
        }
        let skey = cookies[config_1.clubWebCfg.cookieName] || req.params.skey;
        if (!skey) {
            res.send({ code: httpStatus_1.HTTP_STATUS_CODES.UNAUTHORIZED, message: "未登录" });
            return;
        }
        let schema = {
            roleid: { type: Number },
        };
        let params = req.params;
        params.skey = skey;
        await (0, helper_1.checkParams)(req.params, schema);
        let result = await auth_1.AuthClass.check({ clubWebRoleId: params.roleid, skey: params.skey }, "clubWebRoleId");
        if (result.validate) {
            req.session = result.session;
            next();
        }
        else {
            throw result.error;
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=authCheck.js.map
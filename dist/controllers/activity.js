"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addMoment = addMoment;
exports.getCoupleInfoHandler = getCoupleInfoHandler;
const helper_1 = require("../helper");
const util_1 = require("../common/util");
const PyqMoments_1 = require("../models/PyqMoments");
const moment_1 = require("../services/moment");
const activityTopic_1 = require("../services/activityTopic");
const models_1 = require("../models");
const cacheService_1 = require("../common/cacheService");
async function addMoment(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            text: { type: String },
            imgs: { type: Array, required: false },
            skipImgAudit: { type: Number, required: false },
            videos: { type: Array, required: false },
            skipVideoAudit: { type: Number, required: false },
            topicId: { type: Number, required: false },
            emitTopicEvent: { type: Number, required: false }
        };
        let ip = (0, util_1.getIp)(req);
        let params = req.params;
        await (0, helper_1.checkParams)(params, schema);
        let props = { RoleId: params.roleId, Text: params.text, CreateTime: Date.now() };
        let addImg = params.imgs && params.imgs.length > 0;
        let addVideo = params.videos && params.videos.length > 0;
        if (addVideo) {
            props.VideoList = params.videos.join(',');
            if (params.skipVideoAudit) {
                props.VideoAudit = (0, util_1.fillCsvStr)('1', params.videos.length);
            }
        }
        else {
            if (addImg) {
                props.ImgList = params.imgs.join(',');
                if (params.skipImgAudit) {
                    props.ImgAudit = (0, util_1.fillCsvStr)('1', params.imgs.length);
                }
            }
        }
        let mId = await PyqMoments_1.Moment.insert(props);
        if (addVideo && !params.skipVideoAudit) {
            (0, moment_1.auditMomentVideoList)(params.roleId, params.imgs, mId, ip);
        }
        if (!addVideo && addImg && !params.skipImgAudit) {
            (0, moment_1.auditMomentImageList)(params.roleId, params.imgs, mId, ip);
        }
        let emitTopicEvent = params.emitTopicEvent === 1;
        (0, activityTopic_1.processTopicMoment)(params.roleId, mId, params.imgs, params.videos, params.topicId, emitTopicEvent);
        res.send({ code: 0, data: { id: mId } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getCoupleInfo(roleId) {
    const data = { has: false };
    const profile = await models_1.ProfileModel.findOne({ RoleId: roleId }, ['RoleId', 'Wedding']);
    if (profile && profile.Wedding) {
        const weddingInfo = (0, util_1.getJsonInfo)(profile.Wedding);
        if (weddingInfo && weddingInfo.coupleId > 0) {
            data.has = true;
            data.coupleId = weddingInfo.coupleId;
            data.coupleName = weddingInfo.name || "";
        }
    }
    return data;
}
const getCoupleInfoFast = (0, cacheService_1.smartMemorize)(getCoupleInfo, {
    keyGen(roleId) {
        return (0, util_1.cacheKeyGen)("couple_info", { roleId });
    },
    expireSeconds: 3 * 60
});
async function getCoupleInfoHandler(req, res, next) {
    try {
        const schema = {
            roleId: { type: Number },
        };
        const params = req.params;
        await (0, helper_1.checkParams)(params, schema);
        const data = await getCoupleInfoFast(params.roleId);
        res.send({ code: 0, data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=activity.js.map
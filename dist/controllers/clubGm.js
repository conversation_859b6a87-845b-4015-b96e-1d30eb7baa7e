"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateClubHonor = updateClubHonor;
exports.syncClubHonorMatchTable = syncClubHonorMatchTable;
exports.parseClubExcelMatch = parseClubExcelMatch;
exports.syncClubHonorTable = syncClubHonorTable;
exports.useGmAuthChecker = useGmAuthChecker;
exports.isValidGm = isValidGm;
exports.addGm = addGm;
exports.cleanClubHonors = cleanClubHonors;
const helper_1 = require("../helper");
const logger_1 = require("../logger");
const ClubHonorAssignService = require("../services/clubHonorAssign");
const bluebird = require("bluebird");
const ClubHonor_1 = require("../models/ClubHonor");
const util_1 = require("../common/util");
const xlsx = require("node-xlsx");
const authCheck_1 = require("../middlewares/authCheck");
const jwt = require("jwt-simple");
const errorCodes_1 = require("../errorCodes");
const config_1 = require("../common/config");
const redis_1 = require("../common/redis");
const Club_1 = require("../models/Club");
const ClubHonorBind_1 = require("../models/ClubHonorBind");
const clubPlayerHonor_1 = require("../models/clubPlayerHonor");
const CertifiedPlayer_1 = require("../models/CertifiedPlayer");
const _ = require("lodash");
const matches = [
    { id: 1, name: "公平联赛@ClubHonorGPLS" },
    { id: 1, name: "公平模式帮会联赛@ClubHonorGPLS" },
    { id: 2, name: "跨服明星邀请赛@HonorKFMXYQS" },
    { id: 3, name: "怒海争锋@ClubHonorNHZF" },
];
async function updateClubHonor(req, res, next) {
    try {
        const schema = {
            action: { type: String, values: ["parse", "calHonor", "update"] },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const excelPath = req.files?.honorExcel?.path || "";
        if (!excelPath) {
            await bluebird.reject({ message: "俱乐部荣誉excel未上传!" });
        }
        logger_1.logger.info({ files: req.files }, "files");
        const params = req.params;
        let data = null;
        if (params.action === "parse") {
            data = ClubHonorAssignService.parseClubExcelHonors(excelPath);
        }
        else if (params.action === "calHonor") {
            data = ClubHonorAssignService.calculateHonor(excelPath);
        }
        else if (params.action === "update") {
            data = await ClubHonorAssignService.updateHonor(excelPath);
        }
        else {
            // should not go here
        }
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function syncClubHonorMatchTable(req, res, next) {
    try {
        const schema = {
            action: { type: String, values: ["parse", "update"] },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        logger_1.logger.info({ files: req.files }, "files");
        const excelPath = req.files?.matchExcel?.path || "";
        if (!excelPath) {
            await bluebird.reject({ message: "俱乐部比赛excel未上传!" });
        }
        const params = req.params;
        let data = null;
        if (params.action === "parse") {
            data = parseClubExcelMatch(excelPath);
        }
        else if (params.action === "update") {
            data = await syncClubHonorTable(excelPath);
        }
        else {
            // should not go here
        }
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
function parseClubExcelMatch(excelPath) {
    const data = getClubHonorMatchList(excelPath);
    return data;
}
async function syncClubHonorTable(excelPath) {
    const retList = [];
    const data = parseClubExcelMatch(excelPath);
    for (const r of data) {
        const ret = await ClubHonor_1.ClubHonorModel.createOrUpdate(r);
        retList.push(ret);
    }
    const isOk = (0, util_1.isAll)(retList, (r) => r.affectedRows > 0 || r.insertId > 0);
    return { isOk: isOk };
}
const sheetNames = new Set(matches.map((x) => x.name));
function getClubHonorMatchList(excelPath) {
    const result = [];
    const DATA_START_COL = 4;
    const excel = xlsx.parse(excelPath);
    const sheets = excel.filter((s) => sheetNames.has(s.name));
    for (const s of sheets) {
        const competitionType = matches.find((m) => m.name === s.name).id;
        logger_1.logger.info({ name: s.name }, "ProcessSheetName");
        for (let i = 0; i < s.data.length; i++) {
            const row = s.data[i];
            if (i > DATA_START_COL) {
                const subId = _.toNumber((0, util_1.getCell)(row, "A"));
                if (_.isNumber(subId)) {
                    const item = {
                        CompetitionId: competitionType,
                        SubId: subId,
                        WhichTimes: (0, util_1.getCell)(row, "B"),
                        HonorType: (0, util_1.getCell)(row, "C"),
                        Date: (0, util_1.getCell)(row, "E"),
                    };
                    logger_1.logger.debug(item, "ProcessSheetNameRow");
                    if (item.SubId && item.CompetitionId) {
                        result.push(item);
                    }
                }
                else {
                    break;
                }
            }
        }
    }
    return result;
}
function useGmAuthChecker(app, urlPrefix) {
    (0, helper_1.preUseWhenUrlPrefix)(app, urlPrefix, authCheck_1.skipSkeyAuthMiddleware);
    (0, helper_1.useWhenUrlPrefix)(app, urlPrefix, checkGmAuth);
}
const clubGmSetKey = "club_gm_admin_set";
async function isValidGm(gm) {
    if ((0, util_1.contains)(config_1.gmCfg.superAdmins, gm)) {
        return true;
    }
    else {
        const ret = await (0, redis_1.getRedis)().sismemberAsync(clubGmSetKey, gm);
        return ret === redis_1.IExistResult.Exist;
    }
}
async function validateGmAuthToken(token) {
    let gm = "";
    try {
        const info = jwt.decode(token, config_1.JWT_TOKEN_SECRET, false, "HS256");
        gm = info.gm || "";
    }
    catch (err) {
        logger_1.logger.warn({ err, token, JWT_TOKEN_SECRET: config_1.JWT_TOKEN_SECRET }, 'validateGmAuthTokenFail');
        await bluebird.reject(errorCodes_1.ClubGmErrors.AuthTokenNotValid);
    }
    const hasPermission = await isValidGm(gm);
    return hasPermission;
}
async function addGm(gm) {
    const ret = await (0, redis_1.getRedis)().saddAsync(clubGmSetKey, gm);
    return ret;
}
async function checkGmAuth(req, res, next) {
    if (config_1.testCfg.test_env) {
        next();
    }
    else {
        try {
            const authHeader = req.headers["authorization"] || "";
            const fields = authHeader.split(" ");
            if (fields && fields[0] === "Bearer") {
                const permission = await validateGmAuthToken(fields[1]);
                if (permission) {
                    next();
                }
                else {
                    await bluebird.reject(errorCodes_1.ClubGmErrors.GmNotAddToAdmin);
                }
            }
            else {
                await bluebird.reject(errorCodes_1.ClubGmErrors.AuthTokenNotValid);
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
async function cleanClubHonors(req, res, next) {
    try {
        const ret = await Promise.all([
            Club_1.ClubModel.updateByCondition({}, { Honor: 0 }),
            ClubHonorBind_1.ClubHonorBindModel.deleteByCondition({}),
            clubPlayerHonor_1.ClubPlayerHonorBindModel.deleteByCondition({}),
            CertifiedPlayer_1.CertifiedPlayerModel.updateByCondition({}, { Honor: 0 })
        ]);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=clubGm.js.map
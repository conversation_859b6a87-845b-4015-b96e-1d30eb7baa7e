"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncRealName = syncRealName;
exports.queryRealNameByMobile = queryRealNameByMobile;
exports.queryRealNameAndAntiIndulgence = queryRealNameAndAntiIndulgence;
exports.queryGameIdByUrs = queryGameIdByUrs;
exports.loginBehavior = loginBehavior;
exports.logoutBehavior = logoutBehavior;
exports.getCollections = getCollections;
const util_1 = require("../common/util");
const helper_1 = require("../helper");
const logger_1 = require("../logger");
const FcmService = require("../services/fcm");
async function syncRealName(req, res, next) {
    try {
        let schema = {
            username: { type: String },
            product: { type: String },
            idtype: { type: Number },
            idnum: { type: String },
            realname: { type: String },
            userip: { type: String, required: false },
        };
        req.params.realname = (0, util_1.decodeUtf8HexString)(req.params.realname);
        logger_1.fcmLogger.info({ params: req.params }, 'syncRealNameParsedResult');
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await FcmService.syncRealName(req.params);
        res.send(data);
    }
    catch (err) {
        (0, helper_1.ursErrorV2Handler)(err, req, res, next);
    }
}
/**
 * 手机帐号查询实名接口
 */
async function queryRealNameByMobile(req, res, next) {
    try {
        let schema = {
            username: { type: String },
            product: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await FcmService.queryRealNameStatus(req.params);
        res.send(data);
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function queryRealNameAndAntiIndulgence(req, res, next) {
    try {
        let schema = {
            username: { type: String },
            product: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const ip = (0, util_1.getIp)(req);
        const data = await FcmService.queryRealNameAndAntiIndulgence(req.params, ip);
        res.header("content-type", "text/plain");
        res.send(data);
    }
    catch (err) {
        (0, helper_1.ursErrorHandler)(err, req, res, next);
    }
}
async function queryGameIdByUrs(req, res, next) {
    try {
        let schema = {
            username: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        FcmService.queryGameIdByUrsApiCounter.incrBy(1);
        const data = await FcmService.queryGameIdByUrs(req.params);
        res.send({ code: 200, subcode: 0, status: 1, data });
    }
    catch (err) {
        (0, helper_1.ursGameErrorHandler)(err, req, res, next);
    }
}
async function checkBehaviorParams(params) {
    let schema = {
        roleid: { type: Number },
        urs: { type: String },
        ts: { type: Number },
    };
    await (0, helper_1.checkParams)(params, schema);
}
async function loginBehavior(req, res, next) {
    try {
        await checkBehaviorParams(req.params);
        const data = await FcmService.loginBehavior(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function logoutBehavior(req, res, next) {
    try {
        await checkBehaviorParams(req.params);
        const data = await FcmService.logoutBehavior(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getCollections(req, res, next) {
    try {
        let schema = {
            size: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await FcmService.logoutBehavior(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=fcm.js.map
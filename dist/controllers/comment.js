"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addComment = addComment;
exports.delComment = delComment;
exports.listComment = listComment;
exports.likeComment = likeComment;
const helper_1 = require("../helper");
const UserPermission_1 = require("../models/UserPermission");
const MomentService = require("../services/moment");
const CommentService = require("../services/comment");
async function addComment(req, res, next) {
    let roleId = req.params.roleid;
    try {
        await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Comment, '禁止发表评论');
        let schema = {
            roleid: { type: Number },
            id: { type: Number },
            replyid: { type: Number, required: false },
            text: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await MomentService.addComment(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function delComment(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            id: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let data = await MomentService.delComment(req.params);
        res.send({ code: 0, data: { status: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listComment(req, res, next) {
    try {
        let schema = {
            momentId: { type: Number, required: true },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
            orderBy: { type: String, values: ['likes', 'mix'], default: 'mix' }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let result = await CommentService.listComment(params);
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function likeComment(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            id: { type: Number },
            action: { type: String, values: ['do', 'undo'] }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await CommentService.likeCommentDispatcher(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=comment.js.map
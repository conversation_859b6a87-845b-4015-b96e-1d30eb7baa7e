"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.corsPreflight = exports.corsAllow = void 0;
exports.add = add;
exports.getOne = getOne;
exports.updateMomentStatus = updateMomentStatus;
exports.edit = edit;
exports.updateSetting = updateSetting;
exports.list = list;
exports.getLoginInfo = getLoginInfo;
exports.addMoment = addMoment;
exports.delMoment = delMoment;
exports.editMoment = editMoment;
exports.listMoments = listMoments;
exports.listComments = listComments;
exports.listForwards = listForwards;
exports.replayComment = replayComment;
exports.setCommentTop = setCommentTop;
exports.cancelCommentTop = cancelCommentTop;
exports.addComment = addComment;
exports.delComment = delComment;
exports.addForward = addForward;
exports.likeMoment = likeMoment;
exports.cancelLikeMoment = cancelLikeMoment;
exports.setMomentTop = setMomentTop;
exports.cancelMomentTop = cancelMomentTop;
exports.publicMomentAtTime = publicMomentAtTime;
exports.checkLoginUrsPermission = checkLoginUrsPermission;
exports.checkUrsIsSuperAdmin = checkUrsIsSuperAdmin;
exports.checkOpenIdIsSuperAdmin = checkOpenIdIsSuperAdmin;
exports.listTopics = listTopics;
exports.exportMomentDetail = exportMomentDetail;
const helper_1 = require("../helper");
const accounts_1 = require("../services/official_accounts/accounts");
const corsPlugin_1 = require("../common/corsPlugin");
const moments_1 = require("../services/official_accounts/moments");
const MomentService = require("../services/moment");
const activityTopic = require("../services/activityTopic");
const PyqComment_1 = require("../models/PyqComment");
const RoleInfos_1 = require("../models/RoleInfos");
const OffcialAccountAdmin_1 = require("../models/OffcialAccountAdmin");
const atPlayer_1 = require("../services/atPlayer");
const follow_1 = require("../services/follow");
const constants_1 = require("../common/constants");
const openIdAdmin_1 = require("../models/openIdAdmin");
const service_1 = require("../components/officialAccount/service");
const excelTool_1 = require("../common/excelTool");
const moment = require("moment");
const OfficialAccount_1 = require("../models/OfficialAccount");
async function add(req, res, next) {
    try {
        let schema = {
            name: { type: String, maxlen: 7 },
            title: { type: String, required: false, maxlen: 7 },
            signature: { type: String, required: false, maxlen: 30 },
            yinyuan: { type: String, required: false, maxlen: 7 },
            avatar: { type: String, required: false },
            location: { type: String, required: false, maxlen: 6 },
            guild: { type: String, required: false, maxlen: 7 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let roleId = await (0, accounts_1.createAccount)(params);
        await follow_1.OfficialAccountIdCache.refresh();
        res.send({ code: 0, data: { roleId: roleId } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getOne(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, accounts_1.showAccount)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function updateMomentStatus(req, res, next) {
    try {
        let schema = {
            momentId: { type: Number },
            publicStatus: { type: Number, values: [0, 1] }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, moments_1.doUpdateMomentStatus)(params);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function edit(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            name: { type: String, required: false, maxlen: 7 },
            title: { type: String, required: false, maxlen: 7 },
            yinyuan: { type: String, required: false, maxlen: 7 },
            signature: { type: String, required: false, maxlen: 30 },
            avatar: { type: String, required: false },
            location: { type: String, required: false, maxlen: 6 },
            guild: { type: String, required: false, maxlen: 7 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, accounts_1.editAccount)(params);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function updateSetting(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            publicStatus: { type: Number, required: false, values: [0, 1] },
            forceFollow: { type: Number, required: false, values: [0, 1] },
            autoBeHot: { type: Number, required: false, values: [0, 1] }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, accounts_1.updateAccountSetting)(params);
        await follow_1.OfficialAccountIdCache.refresh();
        await (0, OfficialAccount_1.refreshAllAccounts)();
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function list(req, res, next) {
    try {
        let list = await (0, accounts_1.listAccounts)();
        res.send({ code: 0, data: { list: list } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getLoginInfo(req, res, next) {
    let session = req.session || { urs: '' };
    res.send({ code: 0, data: { urs: session.urs } });
}
async function addMoment(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            text: { type: String, required: false },
            imgList: { type: Array, required: false },
            topicId: { type: Number, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let id = await (0, moments_1.createNewMoment)(params);
        res.send({ code: 0, data: { id: id } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function delMoment(req, res, next) {
    try {
        let schema = {
            id: { type: Number },
            roleId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await MomentService.delMomentByOfficial({ roleid: params.roleId, id: params.id });
        res.send({ code: 0, data: { affectedRows: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function editMoment(req, res, next) {
    try {
        let schema = {
            id: { type: Number },
            roleId: { type: Number },
            text: { type: String, required: false },
            imgList: { type: Array, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.modifyMoment)(params);
        res.send({ code: 0, data: { affectedRows: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listMoments(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.getMomentList)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listComments(req, res, next) {
    try {
        let schema = {
            momentId: { type: Number },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
            kw: { type: String, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.getCommentList)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listForwards(req, res, next) {
    try {
        let schema = {
            momentId: { type: Number },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
            kw: { type: String, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.getForwardList)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getFullReplayText(text, replyId) {
    let record = await RoleInfos_1.RoleInfo.findOne({ RoleId: replyId }, ['RoleName']);
    if (record) {
        let prefix = '回复' + (0, atPlayer_1.generateGameAtLink)(replyId, record.RoleName);
        return prefix + text;
    }
    else {
        return text;
    }
}
async function replayComment(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
            replyId: { type: Number },
            text: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let fullText = params.text;
        fullText = await (0, moments_1.transferAtPlayerLink)(fullText);
        if (params.replyId) {
            fullText = await getFullReplayText(fullText, params.replyId);
        }
        let data = await MomentService.addComment({ id: params.momentId, roleid: params.roleId, text: fullText, replyid: params.replyId });
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function setCommentTop(req, res, next) {
    try {
        let schema = {
            commentId: { type: Number }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.doTopComment)(params.commentId);
        res.send({ code: 0, data: { affectedRows: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cancelCommentTop(req, res, next) {
    try {
        let schema = {
            commentId: { type: Number }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await PyqComment_1.Comment.updateByCondition({ Id: params.commentId }, { TopTime: 0 });
        res.send({ code: 0, data: { affectedRows: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function addComment(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
            text: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let text = await (0, moments_1.transferAtPlayerLink)(params.text);
        let data = await MomentService.addComment({ id: params.momentId, roleid: params.roleId, text: text });
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function delComment(req, res, next) {
    try {
        let schema = {
            id: { type: Number },
            roleId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await MomentService.delComment({ roleid: params.roleId, id: params.id });
        res.send({ code: 0, data: { affectedRows: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function addForward(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
            text: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.addMomentForward)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
let corsPlugin = corsPlugin_1.CorsPlugin.create({ origins: /^https?:\/\/.*\.163\.com(:\d+)?/ });
exports.corsAllow = corsPlugin.cors();
exports.corsPreflight = corsPlugin.preflight();
async function likeMoment(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.doLikeMoment)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cancelLikeMoment(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await (0, moments_1.doCancelLikeMoment)(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function setMomentTop(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, moments_1.doTopMoment)(params);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cancelMomentTop(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, moments_1.doCancelTopMoment)(params);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function publicMomentAtTime(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            momentId: { type: Number },
            publicTime: { type: Number }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, moments_1.publicAtTime)(params);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function checkLoginUrsPermission(req, res, next) {
    let urs = req.session.urs;
    try {
        let isAdmin = await OffcialAccountAdmin_1.OfficialAccountAdmin.isAdmin(urs);
        if (isAdmin) {
            next();
        }
        else {
            res.send({ code: -2, msg: 'urs account no permission' });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function checkUrsIsSuperAdmin(req, res, next) {
    let urs = req.session.urs;
    try {
        let isSuperAdmin = await OffcialAccountAdmin_1.OfficialAccountAdmin.isSuperAdmin(urs);
        if (isSuperAdmin) {
            next();
        }
        else {
            res.send({ code: -1, msg: '权限不足，无法进行此操作' });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function checkOpenIdIsSuperAdmin(req, res, next) {
    let openId = req.session.mail;
    try {
        let isSuperAdmin = await openIdAdmin_1.OpenIdAdminModel.checkSuperAdmin(openId);
        if (isSuperAdmin || openId === 'skip_openId_auth') {
            next();
        }
        else {
            res.send({ code: -1, msg: '权限不足，无法进行此操作' });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listTopics(req, res, next) {
    try {
        let data = await activityTopic.listTopicsFromDB(constants_1.TopicListSize);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function exportMomentDetail(req, res, next) {
    try {
        const schema = {
            momentId: { type: Number },
            offset: { type: String, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await (0, service_1.exportMomentDetailData)(params.momentId, params.offset);
        const sheetName = data.offset ? `${data.offset}` : '无更多数据';
        const buff = await (0, excelTool_1.generateOneSheetExcelBuff)(data.list, constants_1.MomentDetailExportCfg, sheetName);
        const fileName = `moment(${params.momentId})${moment().format('YYYY-MM-DD')}`;
        res.writeHead(200, {
            'Content-Type': 'multipart/form-data',
            'Content-Disposition': `form-data;filename="${encodeURIComponent(fileName)}.xlsx"`
        });
        res.write(buff);
        res.end();
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=officialAccounts.js.map
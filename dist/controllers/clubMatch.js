"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listRank = listRank;
const helper_1 = require("../helper");
const ClubMatchService = require("../services/clubMatch");
async function listRank(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            whichTimes: { type: Number, default: 1 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await ClubMatchService.listRanks(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=clubMatch.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNosToken = getNosToken;
exports.checkUrsLogin = checkUrsLogin;
exports.getUrsLoginInfo = getUrsLoginInfo;
exports.checkAuthTokenForOutApi = checkAuthTokenForOutApi;
exports.healthCheck = healthCheck;
const nos = require("../common/nos");
const helper_1 = require("../helper");
const ursCookie_1 = require("../services/ursCookie");
const config = require("../common/config");
const util_1 = require("../common/util");
const logger_1 = require("../logger");
const DBHealth_1 = require("../models/DBHealth");
async function getNosToken(req, res, next) {
    try {
        let token = nos.getToken('nsh', req.params);
        res.send({ code: 0, data: token });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
function removeLeadingNoDigit(str) {
    return str.replace(/^[^\d]+/, '');
}
async function checkUrsLogin(req, res, next) {
    try {
        let result = await (0, ursCookie_1.validateUrsLogin)(req.cookies);
        if (result.validate) {
            // urs接口有历史遗留问题，mobile前面可能有非数字字符
            let mobile = removeLeadingNoDigit(result.data.mobile);
            let data = { urs: result.data.ssn, mobile: mobile };
            req.session = data;
            next();
        }
        else {
            res.send({ code: -2, msg: result.msg });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getUrsLoginInfo(req, res, next) {
    let session = req.session || { urs: '', mobile: '' };
    res.send({ code: 0, data: session });
}
async function checkAuthTokenForOutApi(req, res, next) {
    try {
        let schema = {
            product: { type: String, minlen: 2 },
            time: { type: Number },
            nonce: { type: String, minlen: 4 },
            token: { type: String, }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let sign = [params.product, params.time, params.nonce, config.AUTH_TOKEN_SALT].join('');
        let expToken = (0, util_1.hexMd5)(sign);
        if (expToken === params.token) {
            next();
        }
        else {
            logger_1.logger.warn('check token failed', { params: params, actToken: params.token, expToken: expToken });
            res.send({ code: -10, msg: 'check token failed' });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function healthCheck(req, res, next) {
    try {
        let ret = await (0, DBHealth_1.isHealth)();
        if (!ret.isHealth) {
            res.status(500);
        }
        res.send({ code: 0, data: { isOk: ret.isHealth, msg: ret.msg } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=common.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listTopics = listTopics;
exports.listMoment = listMoment;
const helper_1 = require("../helper");
const activityTopic = require("../services/activityTopic");
const activityTopic_1 = require("../services/activityTopic");
const bluebird = require("bluebird");
const constants_1 = require("../common/constants");
async function listTopics(req, res, next) {
    try {
        let data = await activityTopic.listTopicsFromDB(constants_1.TopicListSize);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listMoment(req, res, next) {
    try {
        let schema = {
            topicId: { type: Number },
            serverId: { type: Number, required: false },
            sort: { type: String, values: [activityTopic_1.ListMomentSortTypes.hot, activityTopic_1.ListMomentSortTypes.new, activityTopic_1.ListMomentSortTypes.image, activityTopic_1.ListMomentSortTypes.follow, activityTopic_1.ListMomentSortTypes.curServer] },
            page: { type: Number, min: 1, default: 1 },
            pageSize: { type: Number, min: 1, max: 20, default: 10 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        if (params.sort === activityTopic_1.ListMomentSortTypes.curServer && !params.serverId) {
            await bluebird.reject({ errorType: 'ParamsInvalid', message: 'serverId missing' });
        }
        let data = await activityTopic.listTopicMoment(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=activityTopic.js.map
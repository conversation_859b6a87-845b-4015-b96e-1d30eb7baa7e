"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.answerMessage = void 0;
exports.addMessage = addMessage;
exports.getMessages = getMessages;
exports.delMessage = delMessage;
const message = require("../services/message");
const UserPermission_1 = require("../models/UserPermission");
const helper_1 = require("../helper");
async function addMessage(req, res, next) {
    let roleId = req.params.roleid;
    try {
        await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Message, '禁止发表留言');
        let data = await message.addMsg(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getMessages(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number, required: true },
            targetid: { type: Number, required: false },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let data = await message.getMsg(req.params);
        data = (0, helper_1.formatResult)(data);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function delMessage(req, res, next) {
    try {
        let data = await message.delMsg(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
exports.answerMessage = addMessage;
//# sourceMappingURL=message.js.map
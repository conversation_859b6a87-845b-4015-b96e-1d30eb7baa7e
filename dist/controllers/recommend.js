"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getHottestMoment = getHottestMoment;
const recommendMoments_1 = require("../services/recommendMoments");
const helper_1 = require("../helper");
const RoleInfos_1 = require("../models/RoleInfos");
async function getHottestMoment(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            date: { type: Date, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let date = new Date(Date.now() - 24 * 3600 * 1000);
        if (req.params.date)
            date = new Date(parseInt(req.params.date));
        let momentList = await (0, recommendMoments_1.getRecommendMoment)(date);
        let roleInfoMap = await RoleInfos_1.RoleInfo.getRoleInfoMap(momentList.map(item => { return item.RoleId; }), ['RoleId', 'RoleName', 'ServerId']);
        let data = fillMomentsWithRoleInfo(momentList, roleInfoMap);
        data.forEach(item => {
            if (item.ServerId === null)
                item.ServerId = -1;
        });
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
function fillMomentsWithRoleInfo(moments, roleMap) {
    return moments.map(item => {
        let roleInfo = roleMap.get(item.RoleId);
        let RoleName = roleInfo ? roleInfo.RoleName : null;
        let ServerId = roleInfo ? roleInfo.ServerId : null;
        return { ...item, RoleName, ServerId };
    });
}
//# sourceMappingURL=recommend.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addEvent = addEvent;
exports.getEvents = getEvents;
exports.queryEvent = queryEvent;
const event = require("../services/event");
const helper_1 = require("../helper");
const auth_1 = require("../services/auth");
async function addEvent(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            targetid: { type: Number },
            type: { type: Number },
            token: { type: String },
            parameter: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        if ((0, auth_1.checkAddEventToken)(params)) {
            let data = await event.addEvent(params);
            res.send({ code: 0, data: data });
        }
        else {
            res.send({ code: -2, msg: 'Check token failed' });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getEvents(req, res, next) {
    try {
        let data = await event.get(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function queryEvent(req, res, next) {
    try {
        let data = await event.query(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=event.js.map
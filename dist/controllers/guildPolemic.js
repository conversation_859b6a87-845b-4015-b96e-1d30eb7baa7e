"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addPolemic = addPolemic;
exports.share = share;
exports.likePolemic = likePolemic;
exports.cancelLikePolemic = cancelLikePolemic;
exports.list = list;
exports.listHot = listHot;
exports.applyAddToken = applyAddToken;
exports.verifyPubToken = verifyPubToken;
exports.getDetail = getDetail;
const helper_1 = require("../helper");
const GuildPolemicService = require("../services/guildPolemic");
const constants_1 = require("../common/constants");
const GuildPolemic_1 = require("../models/GuildPolemic");
const guildPolemic_1 = require("../services/guildPolemic");
const errorCodes_1 = require("../errorCodes");
const logger_1 = require("../logger");
const config_1 = require("../common/config");
const UserPermission_1 = require("../models/UserPermission");
async function addPolemic(req, res, next) {
    let schema = {
        roleid: { type: Number },
        title: { type: String },
        content: { type: String },
        guildId: { type: Number },
        guildName: { type: String },
        toGuildId: { type: Number },
        toGuildName: { type: String },
        toServerName: { type: String },
        visibility: { type: Number, values: [GuildPolemic_1.Visibility.Guild, GuildPolemic_1.Visibility.All] },
        pubToken: { type: String },
        style: { type: Number, values: [constants_1.GuildPolemicStyle.horizontal, constants_1.GuildPolemicStyle.vertical] },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await (0, UserPermission_1.checkPermission)(params.roleid, UserPermission_1.Permission.GuildPolemic, "禁止上传檄文");
        let data = await GuildPolemicService.addPolemic(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function share(req, res, next) {
    let schema = {
        roleid: { type: Number },
        id: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.sharePolemic(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function likePolemic(req, res, next) {
    let schema = {
        roleid: { type: Number },
        id: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.likePolemic(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cancelLikePolemic(req, res, next) {
    let schema = {
        roleid: { type: Number },
        id: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.cancelLikePolemic(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function list(req, res, next) {
    let schema = {
        roleid: { type: Number },
        serverId: { type: Number, required: false },
        guildId: { type: Number, required: false },
        page: { type: Number, min: 1, default: 1 },
        pageSize: { type: Number, min: 1, max: 20, default: 10 },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.list(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listHot(req, res, next) {
    let schema = {
        roleid: { type: Number },
        type: { type: String, values: ["local", "all"] },
        serverId: { type: Number },
        page: { type: Number, min: 1, default: 1 },
        pageSize: { type: Number, min: 1, max: 20, default: 10 },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.listHot(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function applyAddToken(req, res, next) {
    let schema = {
        roleid: { type: Number },
        guildId: { type: String },
        time: { type: String },
        nonce: { type: String },
        token: { type: String },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.applyAddToken(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function verifyPubToken(req, res, next) {
    let schema = {
        pubToken: { type: String },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = guildPolemic_1.PolemicTokenManager.verify(params.pubToken);
        if (config_1.guildPolemicCfg.skipPubToken || ret.validate) {
            next();
        }
        else {
            logger_1.logger.info(ret, "verifyPubTokenFailed");
            res.send(errorCodes_1.PolemicErrors.VerifyTokenFailed);
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getDetail(req, res, next) {
    let schema = {
        roleid: { type: Number },
        id: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GuildPolemicService.getDetail(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=guildPolemic.js.map
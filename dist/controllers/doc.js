"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocController = void 0;
const helper_1 = require("../helper");
const path = require("path");
const fs = require("fs");
const errorCodes_1 = require("../errorCodes");
const config_1 = require("../common/config");
const _ = require("lodash");
const YAML = require("yaml");
class DocController {
    static async getSwagger(req, res, next) {
        try {
            const schema = {
                name: { type: String },
            };
            await (0, helper_1.checkParams)(req.params, schema);
            const name = req.params.name;
            const fullPath = path.resolve(__dirname, `../../doc/${name}`);
            if (!fs.existsSync(fullPath)) {
                throw errorCodes_1.MdErrors.DocNotFound;
            }
            fs.createReadStream(fullPath).pipe(res);
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    static getErrorCodeDesc() {
        const defaultGroup = "默认";
        const lines = ["错误码描述:", "|错误码|描述|业务分组|", "|---|---|----|"];
        const errorCodeKeys = Object.keys(errorCodes_1.errorCode);
        let errInfoList = [];
        for (const ek of errorCodeKeys) {
            const item = errorCodes_1.errorCode[ek];
            const code = item.code;
            const msg = item.message || item.msg;
            const group = item.group || defaultGroup;
            errInfoList.push({ code: code, message: msg, group });
        }
        errInfoList = _.orderBy(errInfoList, ["code", "group"], ["desc", "desc"]);
        for (let i = 0; i < errInfoList.length; i++) {
            const item = errInfoList[i];
            lines.push(`|${item.code}|${item.message}|${item.group}|`);
        }
        return lines.join("\n");
    }
    static getYamlOpenApiInfo() {
        const fileContent = fs.readFileSync(path.resolve(__dirname, "../../doc/swagger.yaml"), "utf-8");
        const openApiDoc = YAML.parse(fileContent);
        if (openApiDoc.info.description) {
            openApiDoc.info.description += "\n\n" + DocController.getErrorCodeDesc();
        }
        const finalContent = JSON.stringify(openApiDoc, null, 2);
        return finalContent;
    }
    static async renderElements(req, res, next) {
        try {
            const body = `
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>${config_1.docCfg.title}</title>
    <!-- Embed elements Elements via Web Component -->
    <script src="https://unpkg.com/@stoplight/elements/web-components.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements/styles.min.css">
  </head>
  <body>

    <elements-api
      apiDescriptionDocument='${DocController.getYamlOpenApiInfo()}'
      router="hash"
      layout="sidebar"
    />

  </body>
</html>
      `;
            res.writeHead(200, {
                "Content-Length": Buffer.byteLength(body),
                "Content-Type": "text/html",
            });
            res.write(body);
            res.end();
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
exports.DocController = DocController;
//# sourceMappingURL=doc.js.map
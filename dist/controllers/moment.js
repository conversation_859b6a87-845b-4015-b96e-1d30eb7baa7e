"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forward = forward;
exports.addMoment = addMoment;
exports.getMomentById = getMomentById;
exports.getMoments = getMoments;
exports.getPickedMomentsData = getPickedMomentsData;
exports.getPickedMoments = getPickedMoments;
exports.getHotMoments = getHotMoments;
exports.getAllHotMoments = getAllHotMoments;
exports.delMoment = delMoment;
exports.likeMoment = likeMoment;
exports.getMomentsCount = getMomentsCount;
exports.listForward = listForward;
const eventBus_1 = require("../eventBus");
const MomentService = require("../services/moment");
const constants_1 = require("../common/constants");
const UserPermission_1 = require("../models/UserPermission");
const helper_1 = require("../helper");
const PyqMomentForward_1 = require("../models/PyqMomentForward");
const RoleInfos_1 = require("../models/RoleInfos");
const momentForward_1 = require("../services/momentForward");
const yunyinLog_1 = require("../services/yunyinLog");
const atPlayer_1 = require("../services/atPlayer");
const identity_1 = require("../services/identity");
const momentCounter_1 = require("../services/momentCounter");
const util_1 = require("../common/util");
const PyqMoments_1 = require("../models/PyqMoments");
const index_1 = require("../models/index");
const service_1 = require("../components/adminMomentPick/service");
const _ = require("lodash");
const ajvCheck_1 = require("../common/ajvCheck");
/**
 * @api {POST} /nsh_md/moment/forward 转发一条心情
 * @apiGroup Moments
 * @apiUse CommonParamFields
 * @apiParam {Number} moment_id 转发的心情ID
 * @apiParam {String} [text] 转发时添加的文本 最大文本长度32
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *      code: 0,
 *      data: {
 *        id: 123
 *      }
 *      msg: "转发成功!"
 *   }
 *
 */
async function forward(req, res, next) {
    const validator = req.paramsValidator
        .param("roleid", { type: Number })
        .param("moment_id", { type: Number })
        .param("text", { required: false, type: String, maxlen: 800 });
    try {
        await validator.validate();
        const roleId = req.params.roleid;
        const text = req.params.text;
        await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Moment, "您已被管理员禁止转发朋友圈状态");
        const momentId = req.params.moment_id;
        const data = await PyqMomentForward_1.MomentForward.forwardMoment(roleId, momentId, text);
        (0, yunyinLog_1.addMomentActionLog)(roleId, momentId, "forward", text);
        eventBus_1.EventBus.emit(eventBus_1.Events.FORWARD_MOMENT, Object.assign({}, data, { hotFactor: PyqMoments_1.MomentModel.checkHotFactorParams(req.params.hotFactor) }));
        (0, momentCounter_1.refreshPlayerMomentsCount)(roleId);
        (0, atPlayer_1.processAtPlayerWhenAddMoment)(roleId, data.momentId, text);
        res.send({ code: 0, data: { id: data.momentId }, msg: "转发成功!" });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function addMoment(req, res, next) {
    const roleId = req.params.roleid;
    const schema = {
        properties: {
            context: { type: "string", maxLength: 1024 },
            visualRange: {
                type: "number",
                enum: [PyqMoments_1.EMomentVisualRange.all, PyqMoments_1.EMomentVisualRange.friend, PyqMoments_1.EMomentVisualRange.self],
            },
            canCommentByAll: { type: "number", enum: [0, 1], default: 1 },
            canForward: { type: "number", enum: [0, 1], default: 1 },
            canCommentByStranger: { type: "number", enum: [0, 1], default: 1 },
        },
    };
    try {
        (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
        await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Moment, "禁止发表心情");
        const ip = (0, util_1.getIp)(req);
        req.params.ip = ip;
        const data = await MomentService.addMoment(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getMomentById(req, res, next) {
    try {
        let data = await MomentService.getOne(req.params);
        data = (0, helper_1.formatResult)(data);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getMoments(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number, required: true },
            targetid: { type: Number, required: false },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await MomentService.get(req.params);
        const list = data.list;
        const count = data.count;
        const result = await (0, identity_1.fillIdentity)(list);
        res.send({ code: 0, data: { list: (0, helper_1.formatResult)(result), count: count } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getPickedMomentsData(params) {
    const { rows, count } = await getPickMomentsBasic(params);
    const list = await MomentService.fillInfo({ roleid: params.roleid, refreshPickInfo: true }, rows);
    const result = await (0, identity_1.fillIdentity)(list);
    const data = { list: (0, helper_1.formatResult)(result), count: count };
    return data;
}
async function getPickMomentsBasic(params) {
    const pickInfos = await (0, service_1.getAllPickInfosFast)();
    const pickIds = pickInfos.map((r) => r.momentId);
    const query = MomentService.publicNormalMomentScope().whereIn("ID", pickIds);
    const countQuery = _.cloneDeep(query);
    let moments = await index_1.MomentModel.smartQuery(query, index_1.MomentCols);
    moments = _.compact(pickIds.map((id) => moments.find((r) => r.ID === id)));
    const rows = (0, util_1.getListByPagination)(moments, params);
    const count = await index_1.MomentModel.countByQuery(countQuery);
    return { rows, count };
}
async function getPickedMoments(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number, required: true },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await getPickedMomentsData(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getHotMoments(req, res, next) {
    try {
        const data = await MomentService.getHot(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getAllHotMoments(req, res, next) {
    try {
        const data = await MomentService.getAllHot(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function delMoment(req, res, next) {
    try {
        const data = await MomentService.delMoment(req.params);
        res.send({ code: 0, data: { status: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function likeMoment(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            id: { type: Number },
            action: { type: String, default: "do" },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await MomentService.likeMoment(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getMomentsCount(req, res, next) {
    try {
        const count = await MomentService.getCount(req.params);
        res.send({ code: 0, data: { count: count } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listForwardMoments(momentId, pagination) {
    const query = PyqMomentForward_1.MomentForward.scope()
        .from(constants_1.TABLE_NAMES.momentForward + " as f")
        .select(["m.ID", "m.Text", "m.RoleId", "m.CreateTime"])
        .innerJoin(constants_1.TABLE_NAMES.moment + " as m", "f.MomentId", "m.ID")
        .where("m.Status", 0)
        .where(function () {
        this.where("f.OriginId", momentId);
        this.orWhere("f.ForwardId", momentId);
    })
        .orderBy("m.CreateTime", "desc");
    const rows = (await PyqMomentForward_1.MomentForward.queryWithPagination(query, pagination));
    return rows;
}
async function listForward(req, res, next) {
    try {
        const schema = {
            momentId: { type: Number, required: false },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const moments = await listForwardMoments(params.momentId, params);
        let list = [];
        const roleIds = moments.map((x) => x.RoleId);
        const roleInfoMap = await RoleInfos_1.RoleInfo.getRoleInfoMap(roleIds, RoleInfos_1.BASIC_INFO_COLS);
        for (const m of moments) {
            const roleInfo = roleInfoMap.get(m.RoleId);
            const item = Object.assign({}, m, roleInfo);
            list.push(item);
        }
        list = (0, helper_1.formatResult)(list);
        const totalCount = await momentForward_1.ForwardCountService.getForwardCount(params.momentId);
        res.send({ code: 0, data: { list: list, count: totalCount } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=moment.js.map
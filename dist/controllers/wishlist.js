"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.add = add;
exports.remove = remove;
exports.notifyBuyAction = notifyBuyAction;
exports.show = show;
exports.fullFillList = fullFillList;
exports.removeOneWish = removeOneWish;
exports.removeAllWish = removeAllWish;
exports.removeOneFullFilled = removeOneFullFilled;
exports.removeAllFullFilled = removeAllFullFilled;
const WishListService = require("../services/wishlist");
const helper_1 = require("../helper");
const config_1 = require("../common/config");
const yunyinLog_1 = require("../services/yunyinLog");
async function add(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            commodityId: { type: Number },
            itemId: { type: Number },
            shopId: { type: Number },
            commodityIndex: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.add(params);
        (0, yunyinLog_1.addToWishListLog)(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function remove(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            commodityId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.remove(params);
        (0, yunyinLog_1.removeToWishListLog)(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function notifyBuyAction(req, res, next) {
    try {
        let schema = {
            commodityId: { type: Number },
            itemId: { type: Number },
            buyerId: { type: Number },
            ownerId: { type: Number },
            buyTime: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.notifyBuyAction(params);
        if (ret.fullFill) {
            (0, yunyinLog_1.fullFillWishListLog)(params);
        }
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function show(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            targetid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.show(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function fullFillList(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            page: { type: Number, min: 1, default: 1 },
            pageSize: { type: Number, min: 1, max: config_1.wishListCfg.maxPageSize, default: 10 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.fullFillList(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function removeOneWish(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            wishId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.wishesRemove(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function removeAllWish(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.wishesRemoveAll(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function removeOneFullFilled(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            wishId: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.fullFillRemove(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function removeAllFullFilled(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await WishListService.fullFillRemoveAll(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=wishlist.js.map
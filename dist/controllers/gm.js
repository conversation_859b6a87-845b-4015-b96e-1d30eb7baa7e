"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCoupleInfo = void 0;
exports.ping = ping;
exports.refreshHotMoment = refreshHotMoment;
exports.cleanCopyMomentsRecord = cleanCopyMomentsRecord;
exports.getMomentHot = getMomentHot;
exports.setCoupleInfo = setCoupleInfo;
const HotMoments = require("../services/HotMomentsCache");
const helper_1 = require("../helper");
const CopyMoment_1 = require("../models/CopyMoment");
const PyqMoments_1 = require("../models/PyqMoments");
const util_1 = require("../common/util");
const models_1 = require("../models");
const activity_1 = require("./activity");
function ping(req, res, next) {
    res.send("PONG");
}
async function refreshHotMoment(req, res, next) {
    let params = req.params;
    try {
        let schema = {
            serverId: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let data = await HotMoments.refreshMoments(params.serverId);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cleanCopyMomentsRecord(req, res, next) {
    let params = req.params;
    try {
        let schema = {
            roleid: { type: Number },
            copyRoleId: { type: Number }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let data = await CopyMoment_1.CopyMomentModel.delete({ FromRoleId: params.copyRoleId, ToRoleId: params.roleid });
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getMomentHot(req, res, next) {
    try {
        let schema = {
            id: { type: Number }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let moment = await PyqMoments_1.MomentModel.findById(req.params.id);
        let hot = moment?.Hot || 0;
        res.send({ code: 0, data: { hot, hotState: (0, util_1.getJsonInfo)(moment.HotState) } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function setCoupleInfo(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            coupleId: { type: Number },
            coupleName: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const { roleId, coupleId, coupleName } = req.params;
        const now = Date.now();
        const weddingInfo = JSON.stringify({ coupleId: coupleId, name: coupleName });
        const ret = await models_1.ProfileModel.createOrUpdate({ RoleId: roleId, UpdateTime: now, Wedding: weddingInfo }, { Wedding: weddingInfo });
        res.send({ code: 0, data: { isOk: ret.affectedRows > 0 } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
exports.getCoupleInfo = activity_1.getCoupleInfoHandler;
//# sourceMappingURL=gm.js.map
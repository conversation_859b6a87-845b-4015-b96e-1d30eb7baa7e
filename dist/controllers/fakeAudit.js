"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendPic = sendPic;
const httpLib = require("../common/request");
const helper_1 = require("../helper");
const constants_1 = require("../common/constants");
const config_1 = require("../common/config");
const logger_1 = require("../logger");
const Bluebird = require("bluebird");
async function sendPic(req, res, next) {
    try {
        let schema = {
            product: { type: String },
            pic_list: { type: Array }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        sendAuditResult(params.product, params.pic_list);
        let ids = params.pic_list.map(r => r.pic_id);
        res.send({ code: 0, data: { picIds: ids } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function sendAuditResult(product, list) {
    try {
        await Bluebird.delay(config_1.fakeAuditCfg.auditTime * 1000);
        let returnList = list.map(item => {
            let status = (Math.random() <= config_1.fakeAuditCfg.passRate) ? constants_1.EAuditStatus.PASS : constants_1.EAuditStatus.Reject;
            let sendPic = Object.assign({}, item, { status: status });
            return sendPic;
        });
        callReturnPicApi(product, returnList);
    }
    catch (err) {
        logger_1.logger.error({ product, list }, 'sendAuditResult');
    }
}
async function callReturnPicApi(product, list) {
    let returnPicUrl = config_1.fakeAuditCfg.returnPicUrl;
    logger_1.logger.info({ returnPicUrl, list }, 'callReturnPicApiStart');
    let auditRet = await httpLib.request({
        method: "POST",
        uri: returnPicUrl,
        body: { product: product, pic_list: list },
        json: true
    });
    logger_1.logger.info({ auditRet }, 'callReturnPicApi');
}
//# sourceMappingURL=fakeAudit.js.map
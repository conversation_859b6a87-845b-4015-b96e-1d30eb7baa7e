"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.add = add;
exports.edit = edit;
exports.list = list;
exports.del = del;
const competitionService = require("../services/competition");
const helper_1 = require("../helper");
async function add(req, res, next) {
    try {
        let schema = {
            year: { type: Number },
            whichTimes: { type: Number, min: 1, },
            type: { type: Number },
            subType: { type: Number, required: false },
            group: { type: Number, required: false },
            rank: { type: Number, required: false },
            info: { type: Object }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let ret = await competitionService.add(req.params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function edit(req, res, next) {
    try {
        let schema = {
            id: { type: Number },
            year: { type: Number, required: false },
            whichTimes: { type: Number, required: false },
            type: { type: Number, required: false },
            subType: { type: Number, required: false },
            group: { type: Number, required: false },
            rank: { type: Number, required: false },
            info: { type: Object, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let ret = await competitionService.edit(req.params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function list(req, res, next) {
    try {
        let schema = {
            type: { type: Number },
            year: { type: Number, required: false },
            whichTimes: { type: Number, required: false },
            subType: { type: Number, required: false },
            group: { type: Number, required: false },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, max: 50 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let ret = await competitionService.list(req.params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function del(req, res, next) {
    try {
        let schema = {
            id: { type: Number }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let ret = await competitionService.del(req.params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=competitionAdmin.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.corsPreflight = exports.corsAllow = void 0;
exports.getLoginInfo = getLoginInfo;
exports.logOut = logOut;
exports.createIdentity = createIdentity;
exports.bindIdentity = bindIdentity;
exports.bindIdentityAdmin = bindIdentityAdmin;
exports.getCurrent = getCurrent;
exports.listByRoleId = listByRoleId;
exports.listAll = listAll;
exports.changeIdentity = changeIdentity;
exports.delByIds = delByIds;
exports.addAdmin = addAdmin;
const helper_1 = require("../helper");
const _ = require("lodash");
const IdentityService = require("../services/identity");
const auth_1 = require("../services/auth");
const redis_1 = require("../common/redis");
const corsPlugin_1 = require("../common/corsPlugin");
const identityCache_1 = require("../services/identityCache");
const constants_1 = require("../common/constants");
async function getLoginInfo(req, res, next) {
    let openId = req.session.openId;
    res.send({ code: 0, data: openId });
}
async function logOut(req, res, next) {
    res.setCookie("common_auth_corp_info", "", { path: "/", domain: ".163.com" });
    res.setCookie("common_auth_corp_token", "", { path: "/", domain: ".163.com" });
    res.send({ code: 0, data: null });
}
async function createIdentity(req, res, next) {
    try {
        let schema = {
            templateId: { type: Number },
            title: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let id = await IdentityService.create(params);
        res.send({ code: 0, data: { id } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function bindIdentity(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            templateId: { type: Number },
            text: { type: String },
            source: { type: String },
            expireTime: { type: Number },
            createTime: { type: Number },
            token: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        if ((0, auth_1.checkAddIdentityToken)(params)) {
            await IdentityService.bind(params.roleid, params);
            res.send({ code: 0, data: null });
        }
        else {
            res.send({ code: -2, msg: "Check token failed" });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function bindIdentityAdmin(req, res, next) {
    try {
        let schema = {
            roleid: { type: String },
            templateId: { type: Number },
            text: { type: String },
            source: { type: String },
            expireTime: { type: Number },
            createTime: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await IdentityService.adminBindByTemplateId(params.roleid, params);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getCurrent(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let result = await IdentityService.getCurrent(params.roleid);
        res.send({ code: 0, data: (0, helper_1.formatResult)(result) });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
let corsPlugin = corsPlugin_1.CorsPlugin.create({ origins: /^https?:\/\/.*\.163\.com(:\d+)?/ });
exports.corsAllow = corsPlugin.cors();
exports.corsPreflight = corsPlugin.preflight();
async function listByRoleId(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            targetid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let list = await identityCache_1.IdentityListCache.get(params.targetid);
        list = IdentityService.filterExpireList(list);
        res.send({ code: 0, data: (0, helper_1.formatResult)(list) });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listAll(req, res, next) {
    try {
        let result = await IdentityService.getAllIdentity();
        res.send({ code: 0, data: (0, helper_1.formatResult)(result) });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function changeIdentity(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            templateId: { type: Number },
            hidden: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let isHide = params.hidden === "true";
        await IdentityService.changeIdentity(params.roleid, params.templateId, isHide);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function delByIds(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number },
            targetid: { type: Array },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await IdentityService.delByIds(params.roleid, params.targetid);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function addAdmin(req, res, next) {
    try {
        let schema = {
            urs: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await addAdminToList(params.urs);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getAdminList() {
    let str = await (0, redis_1.getRedis)().getAsync(constants_1.IdentityAdminKey);
    let arr = [];
    if (!_.isEmpty(str))
        arr = JSON.parse(str);
    return arr;
}
async function addAdminToList(urs) {
    let arr = await getAdminList();
    arr.push({ urs: urs });
    return await setAdminList(_.uniq(arr));
}
async function setAdminList(arr) {
    return await (0, redis_1.getRedis)().setAsync(constants_1.IdentityAdminKey, JSON.stringify(arr));
}
//# sourceMappingURL=identity.js.map
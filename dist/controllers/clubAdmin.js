"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listAsserts = listAsserts;
exports.auditAssert = auditAssert;
exports.auditAssertBatch = auditAssertBatch;
exports.listCertifiedInfo = listCertifiedInfo;
exports.auditCertifiedInfo = auditCertifiedInfo;
exports.auditCertifiedInfoBatch = auditCertifiedInfoBatch;
exports.addTagBatch = addTagBatch;
exports.listPlayerTags = listPlayerTags;
exports.updatePlayerTags = updatePlayerTags;
const helper_1 = require("../helper");
const ClubAdminService = require("../services/clubAdmin");
const constants_1 = require("../common/constants");
const clubUserApply_1 = require("../models/clubUserApply");
const _ = require("lodash");
const paramsValidator_1 = require("../common/paramsValidator");
async function listAsserts(req, res, next) {
    try {
        await (0, helper_1.checkPageParams)(req.params);
        let schema = {
            type: { type: String, values: ['videos', 'images'] },
            auditStatus: { type: Number, required: false },
            kw: { type: String, required: false },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.listAsserts(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function auditAssert(req, res, next) {
    try {
        let schema = {
            type: { type: String, values: ['videos', 'images'] },
            id: { type: Number },
            auditStatus: { type: Number, values: [constants_1.AuditStatues.Pass, constants_1.AuditStatues.Reject, constants_1.AuditStatues.Auditing] },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.auditAssert(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function auditAssertBatch(req, res, next) {
    try {
        let schema = {
            ids: { type: Array, each: { type: Number } },
            auditStatus: { type: Number, values: [constants_1.AuditStatues.Pass, constants_1.AuditStatues.Reject, constants_1.AuditStatues.Auditing] },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.auditAssertBatch(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listCertifiedInfo(req, res, next) {
    try {
        let schema = {
            auditStatus: { type: Number, required: false },
            applyType: { type: Number, required: false, default: clubUserApply_1.ApplyType.Commander },
            reason: { type: Number, required: false },
            kw: { type: String, required: false },
        };
        await (0, helper_1.checkPageParams)(req.params);
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.listCertifiedInfo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
const RejectReasonValues = _.values(clubUserApply_1.RejectReason);
async function auditCertifiedInfo(req, res, next) {
    try {
        let schema = {
            id: { type: Number },
            auditStatus: { type: Number },
            reason: { type: Number, values: RejectReasonValues, required: false },
            reasonMsg: { type: String, required: false },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.auditCertifiedInfo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function auditCertifiedInfoBatch(req, res, next) {
    try {
        let schema = {
            ids: { type: Array, each: { type: Number } },
            auditStatus: { type: Number },
            reason: { type: Number, values: RejectReasonValues, required: false },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.auditCertifiedInfoBatch(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function addTagBatch(req, res, next) {
    try {
        let schema = {
            roleIds: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, each: { type: Number } },
            tag: { type: String, maxlen: 8 },
            rare: { type: String, values: ["SSR", "SR", "F"] },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.addTagBatch(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listPlayerTags(req, res, next) {
    let schema = {
        rares: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, required: false },
        roleId: { type: Number, required: false },
        kw: { type: String, min: 2, required: false },
    };
    try {
        await (0, helper_1.checkPageParams)(req.params);
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.listPlayerTags(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function updatePlayerTags(req, res, next) {
    let schema = {
        roleId: { type: Number },
        rare: { type: String, values: ["SSR", "SR", "F"] },
        tags: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, default: [] },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubAdminService.updatePlayerTags(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=clubAdmin.js.map
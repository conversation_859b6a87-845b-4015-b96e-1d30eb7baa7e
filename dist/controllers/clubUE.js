"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClubStat = getClubStat;
const helper_1 = require("../helper");
const ClubService = require("../services/club");
const paramsValidator_1 = require("../common/paramsValidator");
async function getClubStat(req, res, next) {
    try {
        let schema = {
            great_than: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, maxSize: 10, each: { type: Number } },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getClubStat(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=clubUE.js.map
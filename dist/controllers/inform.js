"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNewsNum = getNewsNum;
exports.getInforms = getInforms;
exports.getAllInforms = getAllInforms;
exports.readAll = readAll;
exports.cleanAll = cleanAll;
exports.getRedDot = getRedDot;
const inform = require("../services/inform");
const helper_1 = require("../helper");
const RedDotService = require("../services/redDot");
async function getNewsNum(req, res, next) {
    try {
        let data = await inform.getNewsNum(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getInforms(req, res, next) {
    try {
        let schema = {
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let data = await inform.getPlayerInforms(req.params);
        data = (0, helper_1.formatResult)(data);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getAllInforms(req, res, next) {
    try {
        let schema = {
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let data = await inform.getAll(req.params);
        data = (0, helper_1.formatResult)(data);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function readAll(req, res, next) {
    try {
        let params = req.params;
        await inform.readAllInforms(params.roleid);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cleanAll(req, res, next) {
    try {
        let params = req.params;
        await inform.cleanAllInforms(params.roleid);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getRedDot(req, res, next) {
    try {
        let params = req.params;
        let data = await RedDotService.check(params.roleid);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=inform.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.add = add;
const playerTransfer_1 = require("../services/playerTransfer");
const helper_1 = require("../helper");
const Transfer_1 = require("../models/Transfer");
const logger_1 = require("../logger");
const errorCodes_1 = require("../errorCodes");
const logger = (0, logger_1.clazzLogger)("controllers/serverTransfer");
async function add(req, res, next) {
    const params = req.params;
    const schema = {
        oldId: { type: Number },
        newId: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        const record = await Transfer_1.TransferModel.exists({ NewId: params.newId, OldId: params.oldId });
        if (record) {
            throw errorCodes_1.errorCode.TransferRecordExist;
        }
        const originId = await Transfer_1.TransferModel.findOriginIdByNewId(params.oldId);
        const props = {
            OldId: params.oldId,
            NewId: params.newId,
            OriginId: originId || params.oldId,
            CreateTime: Date.now(),
        };
        const id = await Transfer_1.TransferModel.insert(props);
        logger.info({ id, oldId: params.oldId, newId: params.newId }, "AddTransferRecordOk");
        await (0, playerTransfer_1.addMigrateJob)(params.oldId, params.newId);
        res.send({ code: 0, data: { id: id } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=serverTransfer.js.map
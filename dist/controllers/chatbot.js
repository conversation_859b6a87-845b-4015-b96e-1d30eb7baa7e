"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatbot = chatbot;
exports.fuxiChatbot = fuxiChatbot;
const chatbotService = require("../services/chatbot");
const helper_1 = require("../helper");
const util_1 = require("../common/util");
async function chatbot(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            question: { type: String },
        };
        await chatbotService.isLimit(req, chatbotService.ChatBotType.jingling);
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        await chatbotService.textCheck(params.question);
        let data = await chatbotService.chatbot(params.question);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function fuxiChatbot(req, res, next) {
    try {
        let schema = {
            question: { type: String },
            current_block: { type: Number }
        };
        await chatbotService.isLimit(req, chatbotService.ChatBotType.fuxi);
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await chatbotService.fuxiChatbot(params);
        res.send((0, util_1.getJsonInfo)(data, data));
    }
    catch (e) {
        (0, helper_1.errorHandler)(e, req, res, next);
    }
}
//# sourceMappingURL=chatbot.js.map
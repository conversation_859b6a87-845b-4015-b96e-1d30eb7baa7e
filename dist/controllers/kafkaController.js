"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaController = void 0;
const constants_1 = require("../common/constants");
const addMonthCardLog_1 = require("../components/cloudGameDuration/logConsumer/addMonthCardLog");
const addYuanbaoLog_1 = require("../components/cloudGameDuration/logConsumer/addYuanbaoLog");
const util_1 = require("../components/cloudGameDuration/logConsumer/util");
const operation_1 = require("../components/cloudGameDuration/operation");
const errorCodes_1 = require("../errorCodes");
const helper_1 = require("../helper");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("controller.kafkaController");
class KafkaControllerClass {
    async onAddYuanbaoLogHandler(req, res, next) {
        try {
            const body = req.body;
            if (typeof body != "string") {
                throw errorCodes_1.BaseErrors.InvalidArgument;
            }
            const gs = (0, util_1.parseGameRawLog)(body);
            const parseRet = (0, addYuanbaoLog_1.parseAddYuanbaoLog)(gs);
            if (!parseRet.isOk) {
                logger.warn({ parseRet }, "ParseAddYuanbaoLogFailed");
                throw errorCodes_1.errorCode.ParseGameLogInvalid;
            }
            const pd = parseRet.data;
            const data = await (0, operation_1.cloudGameDurationNotifyChargeYuanbao)({
                urs: pd.accountName,
                orderId: pd.sn,
                num: pd.yuanbao,
                userType: pd.bCloud ? "cloud" : "normal",
            });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    async onBuyMonthCardLogHandler(req, res, next) {
        try {
            const body = req.body;
            if (typeof body != "string") {
                throw errorCodes_1.BaseErrors.InvalidArgument;
            }
            const gs = (0, util_1.parseGameRawLog)(body);
            const parseRet = (0, addMonthCardLog_1.parseAddMonthCardLog)(gs);
            if (!parseRet.isOk) {
                logger.warn({ parseRet }, "ParseAddMonthCardLogFailed");
                throw errorCodes_1.errorCode.ParseGameLogInvalid;
            }
            const pd = parseRet.data;
            const duration = pd.daysAdded * constants_1.ONE_DAY_SECONDS;
            const data = await (0, operation_1.cloudGameDurationNotifyBuyMonthCard)({
                urs: pd.urs,
                orderId: pd.sn,
                duration,
                serverId: pd.serverId,
                channel: "game",
                buyTime: pd.buyTime,
            });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
exports.KafkaController = new KafkaControllerClass();
//# sourceMappingURL=kafkaController.js.map
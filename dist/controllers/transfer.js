"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncData = syncData;
exports.getSignUpNum = getSignUpNum;
exports.getSignUpList = getSignUpList;
const helper_1 = require("../helper");
const transferSignUpService = require("../services/transferSignUp");
async function syncData(req, res, next) {
    try {
        let schema = {
            name: { type: String },
            payload: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let ret = await transferSignUpService.sync(params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getSignUpNum(req, res, next) {
    try {
        let schema = {
            serverIds: { type: String }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let ret = await transferSignUpService.getSignUpNum(req.params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getSignUpList(req, res, next) {
    try {
        let schema = {
            roleId: { type: Number },
            score: { type: Number },
            serverId: { type: Number },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 20 }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let ret = await transferSignUpService.getSignUpList(req.params);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=transfer.js.map
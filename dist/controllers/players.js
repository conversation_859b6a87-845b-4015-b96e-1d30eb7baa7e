"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setSignatureVoice = exports.setPhoto = void 0;
exports.getProfile = getProfile;
exports.getLocation = getLocation;
exports.changeSign = changeSign;
exports.changeLoc = changeLoc;
exports.setProfileHandler = setProfileHandler;
exports.setPrivacy = setPrivacy;
exports.getRank = getRank;
exports.getRecentVisitor = getRecentVisitor;
exports.getHonorRecords = getHonorRecords;
exports.getHonorNum = getHonorNum;
exports.getWeekStatistic = getWeekStatistic;
exports.getWeekRenQiRewardList = getWeekRenQiRewardList;
exports.updateAvatar = updateAvatar;
exports.deleteAvatar = deleteAvatar;
exports.cleanHistory = cleanHistory;
exports.getRoleJobs = getRoleJobs;
exports.copyMoments = copyMoments;
const auth = require("../services/auth");
const MomentService = require("../services/moment");
const location = require("../services/location");
const PlayerAvatarService = require("../services/playerAvatar");
const profile = require("../services/profile");
const rank = require("../services/rank");
const yunyinLog_1 = require("../services/yunyinLog");
const helper_1 = require("../helper");
const UserPermission_1 = require("../models/UserPermission");
const auth_1 = require("../services/auth");
const cleanHistory_1 = require("../services/cleanHistory");
const eventBus_1 = require("../eventBus");
const util_1 = require("../common/util");
const honorRecords_1 = require("../services/honorRecords");
const WeekStatistic_1 = require("../services/WeekStatistic");
const playerRenQiReward_1 = require("../services/playerRenQiReward");
const recentVisitor_1 = require("../services/recentVisitor");
const paramsValidator_1 = require("../common/paramsValidator");
const PyqProfile_1 = require("../models/PyqProfile");
const RoleJob_1 = require("../services/RoleJob");
async function getProfile(req, res, next) {
    const schema = {
        roleid: { type: Number },
        targetid: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const viewRoleId = params.targetid || params.roleid;
        const data = await profile.getProfile(params.roleid, viewRoleId);
        res.send({ code: 0, data: (0, helper_1.formatResult)(data) });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getLocation(req, res, next) {
    try {
        const data = await location.get(req);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function changeSign(req, res, next) {
    const roleId = req.params.roleid;
    try {
        await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Signature, "禁止修改签名");
        const data = await profile.set(req.params);
        const params = req.params;
        (0, yunyinLog_1.addUpdateSignLog)(roleId, params.signature);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function changeLoc(req, res, next) {
    const roleId = req.params.roleid;
    try {
        await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Location, "禁止修改地理位置");
        const data = await profile.set(req.params);
        const params = req.params;
        (0, yunyinLog_1.addUpdateLocationLog)(roleId, params.location);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function setProfileHandler(req, res, next) {
    try {
        const data = await profile.set(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
exports.setPhoto = setProfileHandler;
exports.setSignatureVoice = setProfileHandler;
async function setPrivacy(req, res, next) {
    try {
        const schema = {
            hideLocation: { required: false, type: paramsValidator_1.PARAM_TYPES.BOOLEAN },
            hideVoiceGender: { required: false, type: paramsValidator_1.PARAM_TYPES.BOOLEAN },
            limitComment: { required: false, type: paramsValidator_1.PARAM_TYPES.BOOLEAN },
            muteNewMoment: { required: false, type: paramsValidator_1.PARAM_TYPES.BOOLEAN },
            limitCrossFriend: { required: false, type: paramsValidator_1.PARAM_TYPES.BOOLEAN },
            hideWishList: { required: false, type: paramsValidator_1.PARAM_TYPES.BOOLEAN },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const privacy = await profile.getPrivacy(params.roleid);
        for (const option of PyqProfile_1.PrivacyOptions) {
            // eslint-disable-next-line no-prototype-builtins
            if (params.hasOwnProperty(option)) {
                privacy[option] = params[option];
            }
        }
        eventBus_1.EventBus.emit(eventBus_1.Events.UPDATE_PRIVACY, { roleId: params.roleid, privacy: privacy });
        const data = await profile.setPrivacy(params.roleid, privacy);
        res.send({ code: 0, data: { status: data.affectedRows } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getRank(req, res, next) {
    try {
        const params = req.params;
        if (!auth.checkRankToken(params)) {
            res.send({ code: -2, msg: "非法Token" });
        }
        else {
            const data = await rank.getRankFun(params);
            res.send({ code: 0, data: data });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getRecentVisitor(req, res, next) {
    try {
        const params = req.params;
        const list = await (0, recentVisitor_1.listRecentVisitors)(params.roleid);
        res.send({ code: 0, data: { list: list } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getHonorRecords(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            targetid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await (0, honorRecords_1.getPlayerHonors)(params.targetid);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getHonorNum(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            targetid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const honorNum = await (0, honorRecords_1.getPlayerHonorNum)(params.targetid);
        res.send({ code: 0, data: { honorNum } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getWeekStatistic(req, res, next) {
    const schema = {
        roleid: { type: Number },
        nonce: { type: String },
        timestamp: { type: Number },
        token: { type: String },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const isPass = await (0, auth_1.checkQueryWeekActionStatToken)(params);
        if (isPass) {
            const result = await (0, WeekStatistic_1.getWeekActionStatistic)(params.roleid);
            res.send({ code: 0, data: result });
        }
        else {
            res.send({ code: -2, msg: "check token failed!" });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getWeekRenQiRewardList(req, res, next) {
    try {
        const params = req.params;
        const result = await (0, playerRenQiReward_1.getWeekRenQiRewardInfo)(params.roleid);
        res.send({ code: 0, data: result });
        await (0, playerRenQiReward_1.markWeekRenqiReward)(params.roleid, result);
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function updateAvatar(req, res, next) {
    const schema = {
        url: { type: String },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        const ip = (0, util_1.getIp)(req);
        const params = req.params;
        await (0, UserPermission_1.checkPermission)(params.roleid, UserPermission_1.Permission.Photo, "禁止修改头像");
        const data = await PlayerAvatarService.updateAvatar(params.roleid, params.url, ip);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function deleteAvatar(req, res, next) {
    try {
        const params = req.params;
        const data = await PlayerAvatarService.deleteAvatar(params.roleid);
        res.send({ code: 0, data: { isOk: data.affectedRows > 0 } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cleanHistory(req, res, next) {
    try {
        const params = req.params;
        const ret = await (0, cleanHistory_1.cleanHistoryRecords)(params.roleid);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getRoleJobs(req, res, next) {
    try {
        const data = {
            jobs: RoleJob_1.JobTable,
            gender: RoleJob_1.GenderTable,
            jobAvatar: RoleJob_1.JobAvatarTable,
        };
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function copyMoments(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            copyRoleId: { type: Number },
        };
        const params = req.params;
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await MomentService.copyPlayerMoments(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=players.js.map
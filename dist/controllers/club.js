"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIcons = getIcons;
exports.getClubList = getClubList;
exports.getDetail = getDetail;
exports.listCertifiedPlayers = listCertifiedPlayers;
exports.getCertifiedPlayerDetail = getCertifiedPlayerDetail;
exports.swapHonorLocation = swapHonorLocation;
exports.restoreHonorLocation = restoreHonorLocation;
exports.syncData = syncData;
exports.getExtraInfos = getExtraInfos;
exports.getMatchStatistics = getMatchStatistics;
exports.getPlayerMatchStatistics = getPlayerMatchStatistics;
exports.getPlayerInfo = getPlayerInfo;
exports.cleanPlayerHonor = cleanPlayerHonor;
const helper_1 = require("../helper");
const paramsValidator_1 = require("../common/paramsValidator");
const ClubService = require("../services/club");
const club_1 = require("../services/club");
const ClubHonorService = require("../services/clubHonor");
const ClubSyncService = require("../services/clubSync");
const CertifiedPlayerService = require("../services/certifiedPlayer");
const constants_1 = require("../common/constants");
async function getIcons(req, res, next) {
    try {
        let schema = {
            club_ids: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, maxSize: 500, each: { type: Number } },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getIcons(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getClubList(req, res, next) {
    try {
        let schema = {
            apply_club_ids: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, maxSize: 100, each: { type: Number }, required: false },
            my_club_id: { type: Number, required: false },
            guild_id: { type: Number, required: false },
            sort_type: { type: Number, values: [club_1.ListClubSortType.HonorPoint, club_1.ListClubSortType.MemberSize, club_1.ListClubSortType.GuildRank] },
            server_id: { type: Number, required: false },
            kw: { type: String, required: false },
        };
        await (0, helper_1.checkPageParams)(req.params);
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getClubList(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getDetail(req, res, next) {
    try {
        let schema = {
            club_id: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getDetail(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listCertifiedPlayers(req, res, next) {
    try {
        let schema = {
            join_status: { type: String, default: "all", values: ["all", "join", "free"] },
            role_type: {
                type: Number,
                default: constants_1.RoleTypeFilter.All,
                values: [constants_1.RoleTypeFilter.All, constants_1.RoleTypeFilter.Operator, constants_1.RoleTypeFilter.Commander, constants_1.RoleTypeFilter.BothCertified],
            },
            kw: { type: String, minlen: 1, required: false },
            page: { type: Number, min: 1, default: 1 },
            pageSize: { type: Number, min: 1, max: 20, default: 10 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await CertifiedPlayerService.list(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getCertifiedPlayerDetail(req, res, next) {
    try {
        let schema = {
            targetid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await CertifiedPlayerService.getDetail(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function swapHonorLocation(req, res, next) {
    try {
        let schema = {
            club_id: { type: Number },
            from: { type: Number },
            to: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubHonorService.swapLocation(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function restoreHonorLocation(req, res, next) {
    try {
        let schema = {
            club_id: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubHonorService.restoreLocation(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function syncData(req, res, next) {
    try {
        let schema = {
            name: { type: String },
            payload: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubSyncService.sync(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getExtraInfos(req, res, next) {
    try {
        let schema = {
            club_id: { type: Number },
            role_ids: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, maxSize: constants_1.ClubCfg.maxSize + 1, each: { type: Number } },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getExtraInfos(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getMatchStatistics(req, res, next) {
    try {
        let schema = {
            club_id: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getMatchStatistics(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getPlayerMatchStatistics(req, res, next) {
    try {
        let schema = {
            roleid: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getPlayerMatchStatistics(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getPlayerInfo(req, res, next) {
    try {
        let schema = {
            roleid: { type: String },
            targetid: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubService.getPlayerInfo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function cleanPlayerHonor(req, res, next) {
    try {
        let schema = {
            roleid: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await ClubHonorService.cleanPlayerHonor(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=club.js.map
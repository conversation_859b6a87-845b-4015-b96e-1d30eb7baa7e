"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.follow = follow;
exports.cancelFollow = cancelFollow;
exports.getFollowList = getFollowList;
exports.getFanList = getFanList;
exports.getRecommendedList = getRecommendedList;
exports.isNeedRecommended = isNeedRecommended;
exports.followFriends = followFriends;
const _ = require("lodash");
const PyqFollow_1 = require("../models/PyqFollow");
const helper_1 = require("../helper");
const event_1 = require("../services/event");
const profile_1 = require("../services/profile");
const util_1 = require("../common/util");
const follow_1 = require("../services/follow");
const OfficialAccount_1 = require("../models/OfficialAccount");
const OfficialFollow = require("../services/official_accounts/follow");
const moment_1 = require("../services/moment");
const FollowService = require("../services/follow");
const paramsValidator_1 = require("../common/paramsValidator");
function follow(req, res, next) {
    let roleId, targetId;
    return req.paramsValidator
        .param("roleid", { type: Number })
        .param("targetid", { type: Number })
        .validate()
        .then(function () {
        roleId = req.params.roleid;
        targetId = req.params.targetid;
        if (OfficialAccount_1.OfficialAccount.isOfficialAccount(targetId)) {
            return OfficialFollow.follow(roleId, targetId);
        }
        else {
            return PyqFollow_1.Follow.follow(roleId, targetId);
        }
    })
        .then(function () {
        return (0, moment_1.refreshHomeMomentCount)(req.params.roleid);
    })
        .then(function () {
        res.send({ code: 0, data: { msg: "OK" } });
    })
        .catch(function (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    });
}
function cancelFollow(req, res, next) {
    let roleId, targetId;
    return req.paramsValidator
        .param("roleid", { type: Number })
        .param("targetid", { type: Number })
        .validate()
        .then(function () {
        roleId = req.params.roleid;
        targetId = req.params.targetid;
        if (OfficialAccount_1.OfficialAccount.isOfficialAccount(targetId)) {
            return OfficialFollow.cancelFollow(roleId, targetId);
        }
        else {
            return PyqFollow_1.Follow.cancelFollow(roleId, targetId);
        }
    })
        .then(function () {
        return (0, moment_1.refreshHomeMomentCount)(req.params.roleid);
    })
        .then(function () {
        res.send({ code: 0, data: null });
    })
        .catch(function (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    });
}
async function getFollowList(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number, required: true },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const result = await (0, follow_1.getFollowPlayers)(params);
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getFanList(req, res, next) {
    try {
        const curRoleId = req.params.roleid;
        const followingIds = await PyqFollow_1.Follow.getFollowerIds(curRoleId);
        const following = await (0, profile_1.getRoleInfo)(followingIds, curRoleId);
        const followingMeIds = await PyqFollow_1.Follow.filterMeFollow(followingIds, curRoleId);
        let players = _.map(following, (r) => {
            const followEach = _.includes(followingMeIds, r.RoleId);
            return {
                RoleId: r.RoleId,
                RoleName: r.RoleName,
                FollowingEach: followEach,
                type: event_1.EventTypes.Following,
                roleInfo: r,
            };
        });
        players = (0, util_1.keysToCamelCase)(players);
        res.send({ code: 0, data: players });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getRecommendedList(req, res, next) {
    try {
        const schema = {
            roleid: { type: String, required: true },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        let result = await (0, follow_1.getRecommendedFollowList)(params);
        result = (0, util_1.keysToCamelCase)(result);
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function isNeedRecommended(req, res, next) {
    try {
        const schema = {
            roleid: { type: String, required: true },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        let result = await (0, follow_1.isNeedRecommend)(params);
        result = (0, util_1.keysToCamelCase)(result);
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function followFriends(req, res, next) {
    try {
        const schema = {
            roleid: { type: String, required: true },
            friend_ids: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, each: { type: Number }, default: [] },
            include_official: { type: Boolean, default: true },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await FollowService.followFriends(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=follow.js.map
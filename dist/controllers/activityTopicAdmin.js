"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ursAdminSet = exports.corsPreflight = exports.corsAllow = void 0;
exports.addTopic = addTopic;
exports.editTopic = editTopic;
exports.updateTopicStatus = updateTopicStatus;
exports.listTopics = listTopics;
exports.listTopicMoment = listTopicMoment;
exports.exportMomentReport = exportMomentReport;
exports.updateMomentStatus = updateMomentStatus;
exports.getTopicStatistic = getTopicStatistic;
exports.getNosToken = getNosToken;
exports.getLoginInfo = getLoginInfo;
exports.checkUrsPermission = checkUrsPermission;
exports.checkAdminPermission = checkAdminPermission;
const _ = require("lodash");
const momentTool = require("moment");
const corsPlugin_1 = require("../common/corsPlugin");
const nosService = require("../common/nos");
const util_1 = require("../common/util");
const PyqActivityTopic_1 = require("../models/PyqActivityTopic");
const PyqActivityTopicMoment_1 = require("../models/PyqActivityTopicMoment");
const PyqMoments_1 = require("../models/PyqMoments");
const RoleInfos_1 = require("../models/RoleInfos");
const helper_1 = require("../helper");
const redisCollection_1 = require("../common/redisCollection");
const excelTool_1 = require("../common/excelTool");
const constants_1 = require("../common/constants");
const Slave_1 = require("../models/Slave");
const config_1 = require("../common/config");
let jwt = require('jwt-simple');
var TopicStatus;
(function (TopicStatus) {
    TopicStatus[TopicStatus["DELETE"] = -1] = "DELETE";
    TopicStatus[TopicStatus["NORMAL"] = 0] = "NORMAL";
    TopicStatus[TopicStatus["TOP"] = 1] = "TOP";
})(TopicStatus || (TopicStatus = {}));
var MomentStatus;
(function (MomentStatus) {
    MomentStatus[MomentStatus["DELETE"] = -1] = "DELETE";
    MomentStatus[MomentStatus["NORMAL"] = 0] = "NORMAL";
    MomentStatus[MomentStatus["TOP"] = 1] = "TOP";
})(MomentStatus || (MomentStatus = {}));
async function addTopic(req, res, next) {
    let schema = {
        name: { type: String },
        banner: { type: String },
        desc: { type: String }
    };
    try {
        let params = req.params;
        await (0, helper_1.checkParams)(req.params, schema);
        let id = await PyqActivityTopic_1.ActivityTopic.insert({ Name: params.name, Banner: params.banner, Desc: params.desc, CreateTime: Date.now() });
        res.send({ code: 0, data: { id: id } });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function editTopic(req, res, next) {
    let schema = {
        id: { type: Number },
        name: { type: String, required: false },
        banner: { type: String, required: false },
        desc: { type: String, required: false }
    };
    try {
        let params = req.params;
        await (0, helper_1.checkParams)(req.params, schema);
        let updateProps = {};
        if (params.banner) {
            updateProps.Banner = params.banner;
        }
        if (params.name) {
            updateProps.Name = params.name;
        }
        if (params.desc) {
            updateProps.Desc = params.desc;
        }
        await PyqActivityTopic_1.ActivityTopic.updateByCondition({ ID: params.id }, updateProps);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function updateTopicStatus(req, res, next) {
    let schema = {
        id: { type: Number },
        status: { type: Number }
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        if (params.status === TopicStatus.TOP) {
            await PyqActivityTopic_1.ActivityTopic.updateByCondition({ ID: params.id }, { status: TopicStatus.TOP, TopTime: Date.now() });
        }
        else {
            await PyqActivityTopic_1.ActivityTopic.updateByCondition({ ID: params.id }, { status: params.status, TopTime: 0 });
        }
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listTopics(req, res, next) {
    const MAX_TOPIC_SIZE = 500;
    try {
        let query = PyqActivityTopic_1.ActivityTopic.scope()
            .select(['ID', 'Name', 'Banner', 'Desc', 'CreateTime', 'Status'])
            .orderBy('Status', 'desc')
            .orderBy('TopTime', 'desc')
            .limit(MAX_TOPIC_SIZE);
        let list = await PyqActivityTopic_1.ActivityTopic.executeByQuery(query);
        let result = (0, util_1.keysToCamelCase)({ list: list });
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getBasicRoleInfos(roleIds) {
    let cols = ['RoleId', 'RoleName'];
    let result = new Map();
    let records = await RoleInfos_1.RoleInfo.find({ RoleId: roleIds }, { cols: cols });
    for (let r of records) {
        result.set(r.RoleId, r);
    }
    return result;
}
async function getMoments(ids) {
    let map = new Map();
    let records = await PyqMoments_1.Moment.find({ ID: ids }, { cols: ['ID', 'Text', 'ImgList', 'HotState', 'CreateTime'] });
    for (let r of records) {
        map.set(r.ID, r);
    }
    return map;
}
async function listTopicMoment(req, res, next) {
    let schema = {
        id: { type: Number },
        status: { type: Number, required: false },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, default: 10 },
        kw: { type: String, required: false }
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let query = getListMomentBaseQuery(params);
        query = query.select(['m.ID', 'm.RoleId', 'm.MomentId', 'm.Status']);
        let rows = await PyqActivityTopicMoment_1.ActivityTopicMoment.queryWithPagination(query, params);
        let roleIds = rows.map(x => x.RoleId);
        let momentIds = rows.map(x => x.MomentId);
        let list = [];
        let roleInfoMap = await getBasicRoleInfos(roleIds);
        let momentMap = await getMoments(momentIds);
        let countQuery = getListMomentBaseQuery(params);
        let totalCount = await PyqActivityTopicMoment_1.ActivityTopicMoment.countByQuery(countQuery);
        let totalPage = Math.ceil(totalCount / params.pageSize);
        let curPage = Math.min(totalPage, params.page);
        for (let r of rows) {
            let roleInfo = roleInfoMap.get(r.RoleId) || { RoleId: r.RoleId, RoleName: '' };
            let moment = momentMap.get(r.MomentId) || { ID: r.MomentId, Text: '', ImgList: '', HotState: '' };
            let imgList = (0, util_1.csvStrToArray)(moment.ImgList);
            let hotState = PyqMoments_1.Moment.formatHotState(moment.HotState);
            let item = {
                ID: r.ID,
                RoleId: r.RoleId,
                RoleName: roleInfo.RoleName,
                Text: moment.Text,
                ImgList: imgList,
                LikeCount: hotState.like,
                CommentCount: hotState.comment,
                Status: r.Status
            };
            list.push(item);
        }
        let result = (0, util_1.keysToCamelCase)({ list: list, totalPage: totalPage, curPage: curPage });
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function exportMomentReport(req, res, next) {
    try {
        let schema = {
            id: { type: Number },
            lastId: { type: Number, required: false }
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let topic = await PyqActivityTopic_1.ActivityTopic.findById(params.id);
        let query = getListMomentBaseQuery(params);
        query.orderBy('m.ID', 'asc')
            .limit(config_1.maxExportTopicMomentSize);
        if (params.lastId) {
            query.where('moment.ID', '>', params.lastId);
        }
        query.innerJoin(constants_1.TABLE_NAMES.moment + ' as moment', 'm.MomentId', 'moment.ID');
        query.innerJoin(constants_1.TABLE_NAMES.roleInfo + ' as r', 'm.RoleId', 'r.RoleId');
        query = query.select(['m.ID', 'm.RoleId', 'm.MomentId', 'm.Status', 'moment.Text', 'moment.ImgList', 'moment.VideoList', 'moment.HotState', 'moment.CreateTime', 'r.RoleName']);
        let rows = await (0, Slave_1.SLAVEExecuteSql)(query.toString());
        let list = [];
        for (let r of rows) {
            let imgList = (0, util_1.csvStrToArray)(r.ImgList);
            let videoList = (0, util_1.csvStrToArray)(r.VideoList);
            let hotState = PyqMoments_1.Moment.formatHotState(r.HotState);
            let item = {
                id: r.MomentId,
                roleId: r.RoleId,
                roleName: r.RoleName,
                text: r.Text.replace(/<[\s\S]*?>/g, ""),
                imgList: imgList,
                videoList,
                likeCount: hotState.like,
                commentCount: hotState.comment,
                forwardCount: hotState.forward,
                status: r.Status == MomentStatus.NORMAL ? "上架" : r.Status == MomentStatus.DELETE ? "下架" : "置顶",
                createTime: r.CreateTime ? momentTool(r.CreateTime).format('YYYY-MM-DD HH:mm:ss') : ""
            };
            list.push(item);
        }
        let buff = await (0, excelTool_1.generateOneSheetExcelBuff)(list, constants_1.TopicExportCfg);
        let filename = `topic(${topic.Name})${momentTool().format('YYYY-MM-DD')}`;
        res.writeHead(200, {
            'Content-Type': 'multipart/form-data',
            'Content-Disposition': `form-data;filename="${encodeURIComponent(filename)}.xlsx"`
        });
        res.write(buff);
        res.end();
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
function getListMomentBaseQuery(params) {
    let query = PyqActivityTopicMoment_1.ActivityTopicMoment.scope()
        .from(PyqActivityTopicMoment_1.ActivityTopicMoment.tableName + ' as m')
        .where('m.TopicId', params.id);
    if (_.isNumber(params.status)) {
        query = query.where('m.Status', params.status);
    }
    if (params.kw) {
        query = query.innerJoin(RoleInfos_1.RoleInfo.tableName + " as r", "m.RoleId", "r.RoleId");
        query.where(function () {
            this.where("m.RoleId", "like", "%" + params.kw + "%")
                .orWhere("r.RoleName", "like", "%" + params.kw + "%");
        });
    }
    return query;
}
async function updateMomentStatus(req, res, next) {
    let schema = {
        ids: { type: Array },
        status: { type: Number }
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        if (params.status === MomentStatus.TOP) {
            await PyqActivityTopicMoment_1.ActivityTopicMoment.updateByCondition({ ID: params.ids }, { status: MomentStatus.TOP, TopTime: Date.now() });
        }
        else {
            await PyqActivityTopicMoment_1.ActivityTopicMoment.updateByCondition({ ID: params.ids }, { status: params.status, TopTime: 0 });
        }
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
let corsPlugin = corsPlugin_1.CorsPlugin.create({ origins: /^https?:\/\/.*\.163\.com(:\d+)?/ });
exports.corsAllow = corsPlugin.cors();
exports.corsPreflight = corsPlugin.preflight();
async function getStatisticInfo(topicId) {
    let allQuery = PyqActivityTopicMoment_1.ActivityTopicMoment.scope().where('TopicId', topicId);
    let downQuery = PyqActivityTopicMoment_1.ActivityTopicMoment.scope().where('TopicId', topicId).where('status', MomentStatus.DELETE);
    let topQuery = PyqActivityTopicMoment_1.ActivityTopicMoment.scope().where('TopicId', topicId).where('status', MomentStatus.TOP);
    return {
        total: await PyqActivityTopicMoment_1.ActivityTopicMoment.countByQuery(allQuery),
        down: await PyqActivityTopicMoment_1.ActivityTopicMoment.countByQuery(downQuery),
        top: await PyqActivityTopicMoment_1.ActivityTopicMoment.countByQuery(topQuery)
    };
}
async function getTopicStatistic(req, res, next) {
    let schema = {
        id: { type: Number },
    };
    try {
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let result = await getStatisticInfo(params.id);
        res.send({ code: 0, data: result });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getNosToken(req, res, next) {
    try {
        const token = nosService.getToken('nsh', req.params);
        res.send({ code: 0, data: token });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getLoginInfo(req, res, next) {
    let openId = req.session.openId;
    res.send({ code: 0, data: openId });
}
exports.ursAdminSet = new redisCollection_1.RedisSet('activity_topic:admin_urs_set');
async function checkUrsPermission(urs) {
    return exports.ursAdminSet.isMember(urs);
}
async function validateLogin(req) {
    const authToken = req.cookies['NODE_ZT_AUTH'] || req.params.NODE_ZT_AUTH;
    let secret = 'aej9x4zCnwBfGxk6LGTw';
    try {
        let decoded = jwt.decode(authToken, secret);
        let urs = decoded.urs;
        let hasPermission = await checkUrsPermission(urs);
        if (hasPermission) {
            return { validate: true, urs: urs };
        }
        else {
            return { validate: false, urs: '' };
        }
    }
    catch (err) {
        return { validate: false, urs: '' };
    }
}
async function checkAdminPermission(req, res, next) {
    try {
        let result = await validateLogin(req);
        if (result.validate) {
            req.session = { urs: result.urs };
            next();
        }
        else {
            res.send({ code: -2, message: 'No Permission' });
        }
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=activityTopicAdmin.js.map
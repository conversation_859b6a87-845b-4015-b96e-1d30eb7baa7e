"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncData = syncData;
const helper_1 = require("../helper");
const GardenSyncService = require("../services/gardenSync");
async function syncData(req, res, next) {
    try {
        let schema = {
            name: { type: String },
            payload: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        let params = req.params;
        let data = await GardenSyncService.sync(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=garden.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonMessageController = void 0;
class CommonMessageController {
    /**
     * 添加留言
     */
    async add(req, res) {
        try {
            // 参数验证
            const { roleType, rolename, text } = req.body;
            if (!roleType || ![1, 2].includes(roleType)) {
                return res.json({
                    code: 400,
                    message: '无效的角色类型',
                    data: null
                });
            }
            if (!rolename || rolename.length < 1 || rolename.length > 14) {
                return res.json({
                    code: 400,
                    message: '角色名长度必须在1-14之间',
                    data: null
                });
            }
            if (!text || text.length < 1 || text.length > 50) {
                return res.json({
                    code: 400,
                    message: '留言内容长度必须在1-50之间',
                    data: null
                });
            }
            // TODO: 调用service层处理业务逻辑
            const id = await this.commonMessageService.add({
                roleType,
                rolename,
                text
            });
            return res.json({
                code: 0,
                message: 'success',
                data: {
                    id
                }
            });
        }
        catch (err) {
            return res.json({
                code: 500,
                message: err.message || '服务器内部错误',
                data: null
            });
        }
    }
    /**
     * 获取推荐留言列表
     */
    async getReccList(req, res) {
        try {
            const { roleid } = req.query;
            if (!roleid) {
                return res.json({
                    code: 400,
                    message: '缺少必要参数roleid',
                    data: null
                });
            }
            // TODO: 调用service层获取推荐列表
            const list = await this.commonMessageService.getReccList(roleid);
            return res.json({
                code: 0,
                message: 'success',
                data: {
                    list
                }
            });
        }
        catch (err) {
            return res.json({
                code: 500,
                message: err.message || '服务器内部错误',
                data: null
            });
        }
    }
}
exports.CommonMessageController = CommonMessageController;
exports.default = new CommonMessageController();
//# sourceMappingURL=commonMessageController.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.login = login;
exports.listRoles = listRoles;
exports.bindRole = bindRole;
exports.getClubList = getClubList;
exports.getClubDetail = getClubDetail;
exports.editClubInfo = editClubInfo;
exports.getLoginInfo = getLoginInfo;
exports.getClubRank = getClubRank;
exports.listCertifiedPlayers = listCertifiedPlayers;
exports.getCertifiedPlayerDetail = getCertifiedPlayerDetail;
exports.applyCommander = applyCommander;
exports.sendApplyCommanderSmsCode = sendApplyCommanderSmsCode;
exports.applyOperator = applyOperator;
exports.editUserInfo = editUserInfo;
exports.getHomeInfo = getHomeInfo;
exports.addVideo = addVideo;
exports.updateVideo = updateVideo;
exports.removeVideo = removeVideo;
exports.getNosToken = getNosToken;
exports.recommendCommander = recommendCommander;
exports.listCompetition = listCompetition;
const ClubWebService = require("../services/clubWeb");
const CompetitionService = require("../services/competition");
const SmsService = require("../services/sms");
const neDun_1 = require("../services/neDun");
const auth_1 = require("../common/auth");
const config = require("../common/config");
const helper_1 = require("../helper");
const ursCookie_1 = require("../services/ursCookie");
const config_1 = require("../common/config");
const paramsValidator_1 = require("../common/paramsValidator");
const util_1 = require("../common/util");
const constants_1 = require("../common/constants");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("controllers/clubWeb");
const clubCookieCfg = config_1.clubWebCfg.cookieCfg;
async function login(req, res, next) {
    try {
        const schema = {
            urs: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const cookies = req.cookies;
        const params = req.params;
        const checkRet = await validateUrsFromCookie(cookies, params.urs);
        const urs = checkRet?.data?.ssn || params.urs;
        const loginInfo = await ClubWebService.getLoginInfo(urs);
        if (checkRet.validate && checkRet.data.ssn) {
            await starSession(res, loginInfo);
        }
        else {
            clearSession(res);
        }
        res.send({ code: 0, data: loginInfo });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function starSession(res, bindInfo) {
    const { urs, roleid, roleName } = bindInfo;
    const authInfo = await auth_1.AuthClass.start("clubWebRole", { clubWebRoleid: roleid, urs }, {
        time: Date.now(),
        urs: urs,
        roleName: roleName,
    });
    const cookieCfg = Object.assign({}, clubCookieCfg, { maxAge: 24 * 3600 });
    res.setCookie(config.clubWebCfg.cookieName, authInfo.skey, cookieCfg);
}
function clearSession(res) {
    res.clearCookie(config.clubWebCfg.cookieName, clubCookieCfg);
}
async function validateUrsFromCookie(cookies, urs) {
    const ursValidate = await (0, ursCookie_1.validateUrsLogin)(cookies, urs);
    if (ursValidate.validate == false) {
        throw { code: -1, msg: "未登录" };
    }
    return ursValidate;
}
async function listRoles(req, res, next) {
    try {
        const schema = {
            urs: { type: String },
            neDunKey: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const cookies = req.cookies;
        if (!config_1.testCfg.skip_neDun_verify) {
            await neDun_1.captchaService.verify(params.neDunKey, req);
        }
        const ursInfo = await validateUrsFromCookie(cookies, params.urs);
        const ret = await ClubWebService.listRoles(params, ursInfo);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function bindRole(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            urs: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const cookies = req.cookies;
        const params = req.params;
        const checkRet = await validateUrsFromCookie(cookies, params.urs);
        if (checkRet.data && checkRet.data.ssn) {
            params.urs = checkRet.data.ssn;
        }
        else {
            logger.warn({ params, checkRet }, "BindRoleNotFoundUrsInCookie");
        }
        const ret = await ClubWebService.bindRole(params, checkRet);
        res.send({ code: 0, data: ret });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getClubList(req, res, next) {
    try {
        const schema = {
            kw: { type: String, required: false },
            gForce: { type: Number, required: false },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.getClubList(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getClubDetail(req, res, next) {
    try {
        const schema = {
            club_id: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.getClubDetail(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function editClubInfo(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            club_id: { type: Number },
            icon: { type: String, required: false },
            intro: { type: String, required: false },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const ip = (0, util_1.getIp)(req);
        params.ip = ip;
        const data = await ClubWebService.editClubInfo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getLoginInfo(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const hasRefer = await ClubWebService.hasQualifiedRefer(params.roleid);
        const data = {
            roleid: req.params.roleid,
            roleName: req.session ? req.session.roleName : null,
            hasRefer: hasRefer,
        };
        res.send({ code: 0, data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getClubRank(req, res, next) {
    try {
        const schema = {
            size: { type: Number, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.getClubRank(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listCertifiedPlayers(req, res, next) {
    try {
        const schema = {
            role_type: { type: String, values: ["operator", "commander"] },
            kw: { type: String, required: false },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.listCertifiedPlayers(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getCertifiedPlayerDetail(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.getCertifiedPlayerDetail(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
const BasicApplySchema = {
    urs: { type: String },
    roleid: { type: Number },
    nickname: { type: String },
    avatar: { type: String, required: false },
    realName: { type: String },
    idCard: { type: String },
    gender: { type: Number },
    email: { type: String },
    expertClazz: { type: String },
    phone: { type: String },
};
async function applyCommander(req, res, next) {
    try {
        await (0, helper_1.checkParams)(req.params, BasicApplySchema);
        const schema = {
            videos: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, minlen: 1, maxlen: 5, required: false },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.applyCommander(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function sendApplyCommanderSmsCode(req, res, next) {
    try {
        const schema = {
            neDunKey: { type: String },
            phone: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await SmsService.sendSmsCode(constants_1.ApplyCommanderSmsName, params.phone, params.neDunKey);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function applyOperator(req, res, next) {
    try {
        await (0, helper_1.checkParams)(req.params, BasicApplySchema);
        const schema = {
            videos: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, minlen: 1, maxlen: 5, default: [] },
            images: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, minlen: 1, maxlen: 5 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.applyOperator(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function editUserInfo(req, res, next) {
    try {
        const schema = {
            gender: { type: Number, required: false },
            email: { type: String, required: false },
            avatar: { type: String, required: false },
            phone: { type: String, required: false },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const ip = (0, util_1.getIp)(req);
        params.ip = ip;
        const data = await ClubWebService.editUserInfo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function getHomeInfo(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.getHomeInfo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function addVideo(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            applyId: { type: Number },
            url: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.addVideo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function updateVideo(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            id: { type: Number },
            url: { type: String },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.updateVideo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function removeVideo(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            id: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await ClubWebService.removeVideo(params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
function formatToFeNosToken(token) {
    const sign = token.token.replace("UPLOAD ", "");
    const prefix = token.prefix.replace(/\/$/, "");
    const feToken = {
        put: {
            Bucket: token.bucketname,
            Object: token.objectname,
            Expires: token.expires,
        },
        prefix: prefix,
        sign: sign,
    };
    return feToken;
}
async function getNosToken(req, res, next) {
    try {
        const schema = {
            subDir: { type: String, default: "upload" },
            type: { type: String, values: constants_1.NOS_ALLOW_EXT_NAMES },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await ClubWebService.getNosToken(req.params);
        const feToken = formatToFeNosToken(data);
        res.send({ code: 1, data: feToken });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function recommendCommander(req, res, next) {
    try {
        const schema = {
            roleid: { type: Number },
            targetid: { type: Number },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await ClubWebService.recommendCommander(req.params);
        res.send({ code: 0, data: data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function listCompetition(req, res, next) {
    try {
        const schema = {
            type: { type: Number },
            year: { type: Number, required: false },
            whichTimes: { type: Number, required: false },
            subType: { type: Number, required: false },
            group: { type: Number, required: false },
            page: { type: Number, default: 1 },
            pageSize: { type: Number, default: 1000 },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const data = await CompetitionService.listByWeb(req.params);
        res.send({ code: 0, data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=clubWeb.js.map
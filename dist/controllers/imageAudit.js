"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.returnPic = returnPic;
exports.validateIp = validateIp;
exports.sendPic = sendPic;
const config_1 = require("../common/config");
const helper_1 = require("../helper");
const imageAudit_1 = require("../services/imageAudit");
const ipLimitChecker_1 = require("../middlewares/ipLimitChecker");
const logger_1 = require("../logger");
const ajvCheck_1 = require("../common/ajvCheck");
const logger = (0, logger_1.clazzLogger)("imageAudit.operation");
async function returnPic(req, res, next) {
    try {
        const schema = {
            product: { type: String },
            pic_list: { type: Array },
        };
        await (0, helper_1.checkParams)(req.params, schema);
        const params = req.params;
        const data = await (0, imageAudit_1.handlePicAudit)(params);
        res.send({ code: 0, data });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function validateIp(req, res, next) {
    return (0, ipLimitChecker_1.ipLimitChecker)(config_1.auditCfg.ipWhiteList)(req, res, next);
}
async function sendPic(req, res, next) {
    try {
        const schema = {
            product: { type: String },
            pic_list: { type: Array },
        };
        await (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
        const params = req.params;
        logger.info({ params }, "sendPic");
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=imageAudit.js.map
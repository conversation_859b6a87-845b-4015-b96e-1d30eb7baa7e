"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DBNode = void 0;
exports.execute = execute;
exports.executeWithParams = executeWithParams;
exports.getPoolStatus = getPoolStatus;
exports.close = close;
exports.createDBClient = createDBClient;
exports.getCrossCommDbClient = getCrossCommDbClient;
const mysql = require("mysql");
const _ = require("lodash");
const bluebird = require("bluebird");
const config = require("../common/config");
const logger_1 = require("../logger");
// 创建 logger 实例
const logger = (0, logger_1.clazzLogger)("conn.crossCommDB");
// 定义数据库节点枚举
var DBNode;
(function (DBNode) {
    DBNode["Master"] = "MASTER";
})(DBNode || (exports.DBNode = DBNode = {}));
// 定义查询类型枚举
var DBQueryType;
(function (DBQueryType) {
    DBQueryType["SELECT"] = "SELECT";
    DBQueryType["CREATE"] = "CREATE";
    DBQueryType["UPDATE"] = "UPDATE";
    DBQueryType["DELETE"] = "DELETE";
    DBQueryType["OTHER"] = "OTHER";
})(DBQueryType || (DBQueryType = {}));
// 定义连接池集群
let poolCluster = null;
/**
 * 验证数据库配置参数
 *
 * @param {Object} dbConfig 数据库配置
 * @returns {boolean} 配置是否有效
 */
function validateDbConfig(dbConfig) {
    if (!dbConfig) {
        logger.error("Database configuration is missing");
        return false;
    }
    // 检查必要的配置参数
    const requiredFields = ["host", "user", "password", "database"];
    for (const field of requiredFields) {
        if (!dbConfig[field]) {
            logger.error(`Database configuration is missing required field: ${field}`);
            return false;
        }
    }
    // 检查连接限制是否合理
    if (!dbConfig.connectionLimit || dbConfig.connectionLimit < 1) {
        logger.warn("Database connectionLimit is not set or invalid, using default: 10");
        dbConfig.connectionLimit = 10;
    }
    // 检查端口是否合理
    if (!dbConfig.port || dbConfig.port < 1 || dbConfig.port > 65535) {
        logger.warn("Database port is not set or invalid, using default: 3306");
        dbConfig.port = 3306;
    }
    // 检查字符集
    if (!dbConfig.charset) {
        logger.warn("Database charset is not set, using default: utf8mb4");
        dbConfig.charset = "utf8mb4";
    }
    return true;
}
/**
 * 初始化连接池集群，使用单例模式
 * 确保只创建一次连接池
 */
const getPoolCluster = _.once(function getPoolCluster() {
    const poolCluster = mysql.createPoolCluster({
        canRetry: true,
        removeNodeErrorCount: 5, // 移除节点前的错误次数
        restoreNodeTimeout: 10000, // 尝试恢复节点的时间间隔（毫秒）
        defaultSelector: "RR", // 使用轮询方式选择连接
    });
    // 验证跨服通信数据库配置，如果不合法则尝试使用主配置
    let dbConfig = config.crossCommCfg.db;
    let isCrossCommDbValid = validateDbConfig(dbConfig);
    // 如果跨服通信数据库配置不合法，尝试使用主数据库配置作为fallback
    if (!isCrossCommDbValid) {
        logger.warn("CrossCommDB configuration is invalid, trying to use main DB config as fallback");
        dbConfig = config.db;
        isCrossCommDbValid = validateDbConfig(dbConfig);
    }
    // 添加主节点
    if (isCrossCommDbValid) {
        try {
            poolCluster.add(DBNode.Master, dbConfig);
            logger.info({
                host: dbConfig.host,
                database: dbConfig.database,
                connectionLimit: dbConfig.connectionLimit,
                isFallback: dbConfig === config.db
            }, "crossCommDbMasterNodeAdded");
        }
        catch (err) {
            logger.error({ error: err }, "failedToAddCrossCommDbMasterNode");
            throw new Error(`Failed to initialize CrossCommDB: ${err.message}`);
        }
    }
    else {
        throw new Error("CrossCommDB configuration is invalid and fallback to main DB config also failed");
    }
    // 监听错误事件
    poolCluster.on("remove", function (nodeId) {
        logger.error({ nodeId }, "crossCommDbNodeRemovedFromPool");
        // 尝试在一段时间后重新添加节点
        setTimeout(() => {
            try {
                if (nodeId === DBNode.Master && validateDbConfig(config.crossCommCfg.db)) {
                    poolCluster.add(nodeId, config.crossCommCfg.db);
                    logger.info({ nodeId }, "crossCommDbNodeRestoredToPool");
                }
            }
            catch (err) {
                logger.error({ nodeId, error: err }, "failedToRestoreCrossCommDbNode");
            }
        }, 30000); // 30秒后尝试重新添加
    });
    // 添加连接获取事件监听
    poolCluster.on("connection", function (connection) {
        logger.debug({ threadId: connection.threadId }, "newConnectionEstablished");
        // 设置连接错误处理
        connection.on("error", function (err) {
            logger.error({ error: err, threadId: connection.threadId }, "crossCommDbConnectionError");
        });
    });
    return poolCluster;
});
/**
 * 根据SQL语句判断查询类型
 *
 * @param {string} sql SQL语句
 * @returns {DBQueryType} 查询类型
 */
function getDbQueryTypeBySql(sql) {
    const upperSql = sql.trim().toUpperCase();
    if (upperSql.startsWith("SELECT")) {
        return DBQueryType.SELECT;
    }
    else if (upperSql.startsWith("INSERT") || upperSql.startsWith("REPLACE")) {
        return DBQueryType.CREATE;
    }
    else if (upperSql.startsWith("UPDATE")) {
        return DBQueryType.UPDATE;
    }
    else if (upperSql.startsWith("DELETE")) {
        return DBQueryType.DELETE;
    }
    else {
        return DBQueryType.OTHER;
    }
}
/**
 * 根据SQL语句判断应该使用哪个数据库节点
 *
 * @param {string} sql SQL语句
 * @returns {DBNode} 数据库节点
 */
function getPreferDbNodeBySql(sql) {
    // 在当前实现中，我们只有一个主节点，所以总是返回主节点
    return DBNode.Master;
}
/**
 * 执行SQL语句
 *
 * @param {string} sql SQL语句
 * @param {Object} option 选项
 * @returns {Promise<any>} 查询结果
 */
function execute(sql, option) {
    // 检查连接池是否已初始化
    if (!poolCluster) {
        try {
            poolCluster = getPoolCluster();
        }
        catch (err) {
            return Promise.reject(err);
        }
    }
    // 检查SQL语句是否为空
    if (!sql || sql.trim() === "") {
        return Promise.reject(new Error("SQL statement cannot be empty"));
    }
    // 根据SQL语句确定首选数据库节点
    const preferDb = getPreferDbNodeBySql(sql);
    option = _.defaults(option || {}, { dbNode: preferDb, timeout: 30000 }); // 默认30秒超时
    // 记录开始时间，用于计算查询耗时
    const timeStart = Date.now();
    // 获取查询池
    const queryPool = poolCluster.of(option.dbNode);
    return new bluebird((resolve, reject) => {
        // 创建查询选项
        const queryOptions = {
            sql: sql,
            timeout: option.timeout, // 设置查询超时
        };
        queryPool.query(queryOptions, (err, rows) => {
            // 计算查询耗时
            const timeEnd = Date.now();
            const queryTime = timeEnd - timeStart;
            // 记录慢查询
            if (queryTime > 1000) { // 超过1秒的查询视为慢查询
                logger.warn({
                    sql,
                    queryTime,
                    dbNode: option?.dbNode,
                }, "crossCommDbSlowQuery");
            }
            // 处理错误
            if (err) {
                // 区分不同类型的错误
                if (err.code === 'PROTOCOL_CONNECTION_LOST') {
                    logger.error({
                        sql,
                        error: err.message,
                        dbNode: option?.dbNode,
                    }, "crossCommDbConnectionLost");
                }
                else if (err.code === 'ER_ACCESS_DENIED_ERROR') {
                    logger.error({
                        sql,
                        error: err.message,
                        dbNode: option?.dbNode,
                    }, "crossCommDbAccessDenied");
                }
                else if (err.code === 'ETIMEDOUT' || err.code === 'ECONNREFUSED') {
                    logger.error({
                        sql,
                        error: err.message,
                        dbNode: option?.dbNode,
                    }, "crossCommDbConnectionFailed");
                }
                else if (err.code === 'ER_LOCK_WAIT_TIMEOUT') {
                    logger.error({
                        sql,
                        error: err.message,
                        dbNode: option?.dbNode,
                    }, "crossCommDbLockWaitTimeout");
                }
                else {
                    logger.error({
                        sql,
                        error: err.message,
                        dbNode: option?.dbNode,
                    }, "crossCommDbQueryError");
                }
                return reject(err);
            }
            // 记录调试信息
            if (config.testCfg.db_debug) {
                const queryType = getDbQueryTypeBySql(sql);
                logger.debug({
                    sql,
                    queryTime,
                    queryType,
                    dbNode: option?.dbNode,
                    resultSize: Array.isArray(rows) ? rows.length : 1,
                }, "crossCommDbQuery");
                // 如果开启了显示结果的调试选项，则记录结果
                if (config.testCfg.db_debug_show_result) {
                    logger.debug({ results: rows }, "crossCommDbQueryResult");
                }
            }
            resolve(rows);
        });
    });
}
/**
 * 执行带参数的SQL语句
 *
 * @param {string} sql SQL语句
 * @param {Array|Object} values 参数值
 * @param {Object} option 选项
 * @returns {Promise<any>} 查询结果
 */
function executeWithParams(sql, values, option) {
    // 检查连接池是否已初始化
    if (!poolCluster) {
        try {
            poolCluster = getPoolCluster();
        }
        catch (err) {
            return Promise.reject(err);
        }
    }
    // 检查SQL语句是否为空
    if (!sql || sql.trim() === "") {
        return Promise.reject(new Error("SQL statement cannot be empty"));
    }
    // 根据SQL语句确定首选数据库节点
    const preferDb = getPreferDbNodeBySql(sql);
    option = _.defaults(option || {}, { dbNode: preferDb, timeout: 30000 }); // 默认30秒超时
    // 记录开始时间，用于计算查询耗时
    const timeStart = Date.now();
    // 获取查询池
    const queryPool = poolCluster.of(option.dbNode);
    return new bluebird((resolve, reject) => {
        // 创建查询选项
        const queryOptions = {
            sql: sql,
            values: values,
            timeout: option.timeout, // 设置查询超时
        };
        queryPool.query(queryOptions, (err, rows) => {
            // 计算查询耗时
            const timeEnd = Date.now();
            const queryTime = timeEnd - timeStart;
            // 记录慢查询
            if (queryTime > 1000) { // 超过1秒的查询视为慢查询
                logger.warn({
                    sql,
                    queryTime,
                    dbNode: option?.dbNode,
                }, "crossCommDbSlowQueryWithParams");
            }
            // 处理错误
            if (err) {
                logger.error({
                    sql,
                    error: err.message,
                    dbNode: option?.dbNode,
                }, "crossCommDbQueryWithParamsError");
                return reject(err);
            }
            // 记录调试信息
            if (config.testCfg.db_debug) {
                const queryType = getDbQueryTypeBySql(sql);
                logger.debug({
                    sql,
                    queryTime,
                    queryType,
                    dbNode: option?.dbNode,
                    resultSize: Array.isArray(rows) ? rows.length : 1,
                }, "crossCommDbQueryWithParams");
                // 如果开启了显示结果的调试选项，则记录结果
                if (config.testCfg.db_debug_show_result) {
                    logger.debug({ results: rows }, "crossCommDbQueryWithParamsResult");
                }
            }
            resolve(rows);
        });
    });
}
/**
 * 获取连接池状态
 *
 * @returns {Object} 连接池状态
 */
function getPoolStatus() {
    if (!poolCluster) {
        return { initialized: false };
    }
    try {
        // 获取连接池状态
        const status = {
            initialized: true,
            nodes: {}
        };
        // 注意：mysql 包没有直接提供获取连接池状态的方法
        // 这里只返回基本信息
        return status;
    }
    catch (err) {
        logger.error({ error: err }, "failedToGetPoolStatus");
        return { initialized: true, error: err.message };
    }
}
/**
 * 关闭数据库连接池
 * 在应用程序关闭时调用此方法以正确释放资源
 */
function close() {
    return new Promise((resolve, reject) => {
        if (!poolCluster) {
            return resolve();
        }
        poolCluster.end((err) => {
            if (err) {
                logger.error({ error: err }, "errorClosingCrossCommDbPoolCluster");
                return reject(err);
            }
            logger.info({}, "crossCommDbPoolClusterClosedSuccessfully");
            poolCluster = null;
            resolve();
        });
    });
}
/**
 * 创建并返回一个符合 DBClient 接口的数据库客户端对象
 *
 * @param {DBNode} [defaultNode=DBNode.Master] 默认数据库节点
 * @returns {DBClient} 数据库客户端对象
 */
function createDBClient(defaultNode = DBNode.Master) {
    // 确保连接池已初始化
    if (!poolCluster) {
        try {
            poolCluster = getPoolCluster();
        }
        catch (err) {
            throw new Error(`Failed to initialize database pool: ${err.message}`);
        }
    }
    return {
        /**
         * 执行 SQL 语句
         *
         * @param {string} sql SQL 语句
         * @param {Object} [option] 选项
         * @returns {Promise<any>} 查询结果
         */
        execute: (sql, option) => {
            const dbNode = option?.dbNode || defaultNode;
            return execute(sql, {
                dbNode: dbNode,
                timeout: option?.timeout
            });
        }
    };
}
// 保存 DBClient 单例
let dbClientInstance = null;
/**
 * 获取 DBClient 单例实例
 * 确保只初始化一次
 *
 * @param {DBNode} [defaultNode=DBNode.Master] 默认数据库节点
 * @returns {DBClient} 数据库客户端单例对象
 */
function getCrossCommDbClient(defaultNode = DBNode.Master) {
    if (!dbClientInstance) {
        dbClientInstance = createDBClient(defaultNode);
        logger.info({ defaultNode }, "DBClientInstanceCreated");
    }
    return dbClientInstance;
}
//# sourceMappingURL=crossCommDB.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyUserService = void 0;
const logger_1 = require("../../logger");
const astrologyUserModel_1 = require("../../models/astrology/astrologyUserModel");
const moment = require("moment");
const astrologyRoleInfoModel_1 = require("../../models/astrology/astrologyRoleInfoModel");
const errorCodes_1 = require("../../errorCodes");
const astrologyUserDailyForecastModel_1 = require("../../models/astrology/astrologyUserDailyForecastModel");
const config_all_1 = require("../../common/config.all");
const errorCode_1 = require("../../errors/errorCode");
const bazaarRatingService_1 = require("./bazaarRatingService");
const logger = (0, logger_1.clazzLogger)("astrology.userService");
class AstrologyUserService {
    constructor() {
        this.astrologyUserModel = astrologyUserModel_1.AstrologyUserModel.getInstance();
        this.astrologyRoleInfoModel = astrologyRoleInfoModel_1.AstrologyRoleInfoModel.getInstance();
        this.astrologyUserDailyForecastModel = astrologyUserDailyForecastModel_1.AstrologyUserDailyForecastModel.getInstance();
    }
    static getInstance() {
        if (!AstrologyUserService.instance) {
            AstrologyUserService.instance = new AstrologyUserService();
        }
        return AstrologyUserService.instance;
    }
    async getProfile(ctx, roleId) {
        const r = await this.astrologyUserModel.findOne({ RoleId: roleId });
        return r;
    }
    convertLocationToPlaceStr(place) {
        return `${place.province}-${place.city}-${place.district}`;
    }
    fromPlaceStrToLocation(placeStr) {
        const [province, city, district] = placeStr.split('-');
        return {
            province,
            city,
            district,
        };
    }
    convertToUpdateProps(params) {
        const updateProps = {};
        if (params.gender >= 0) {
            updateProps.Gender = params.gender;
        }
        if (params.birthTime) {
            updateProps.BirthTime = this.convertToBirthTimeInt(params.birthTime);
        }
        if (params.birthPlace) {
            updateProps.BirthPlace = this.convertLocationToPlaceStr(params.birthPlace);
        }
        if (params.currentPlace) {
            updateProps.CurrentPlace = this.convertLocationToPlaceStr(params.currentPlace);
        }
        if (params.astroLevel >= 0) {
            updateProps.AstroLevel = params.astroLevel;
        }
        if (params.astroType >= 0) {
            updateProps.AstroType = params.astroType;
        }
        return updateProps;
    }
    async updateProfile(ctx, roleId, params) {
        const r = await this.astrologyUserModel.findOne({ RoleId: roleId }, ['RoleId']);
        const now = Date.now();
        const updateProps = this.convertToUpdateProps(params);
        if (Object.keys(updateProps).length === 0) {
            logger.warn({ ctx, roleId, params }, "updateProfileNoChange");
            return;
        }
        if (r && r.RoleId) {
            const upRet = await this.astrologyUserModel.updateByCondition({ RoleId: roleId }, updateProps);
            logger.info({ ctx, roleId, params, updateProps, upRet }, "updateProfileOK");
        }
        else {
            const addRet = await this.astrologyUserModel.createOrUpdate({
                RoleId: roleId,
                ...updateProps,
                CreateTime: now,
                UpdateTime: now,
            }, {
                ...updateProps,
                UpdateTime: now,
            });
            logger.info({ ctx, roleId, params, updateProps, addRet }, "updateProfileOK");
        }
    }
    async registerUser(ctx, roleId, params) {
        const now = Date.now();
        const updateProps = this.convertToUpdateProps(params);
        await this.astrologyUserModel.createOrUpdate({
            RoleId: params.roleid,
            ...updateProps,
            CreateTime: now,
            UpdateTime: now,
        }, {
            ...updateProps,
            UpdateTime: now,
        });
        logger.info({ ctx, roleId, params, updateProps }, "RegisterUserOK");
        const resp = {
            roleId: params.roleid,
            createTime: now,
        };
        return resp;
    }
    convertToBirthTimeInt(birthTime) {
        return moment(birthTime).valueOf();
    }
    convertToBirthTimeStr(birthTime) {
        if (birthTime <= 0) {
            return "";
        }
        return moment(birthTime).format('YYYY-MM-DD HH:mm');
    }
    /**
     * 处理评分事件
     * @param ctx
     * @param roleId
     * @param rating
     * @returns
     */
    async updateRatingStat(ctx, roleId, rating, ts) {
        try {
            const rawQuery = this.astrologyUserModel.raw(`
                INSERT INTO nsh_astrology_user (
                    RoleId,
                    Gender,
                    BirthTime,
                    BirthPlace,
                    CurrentPlace,
                    RatingSum,
                    RatingCount,
                    CreateTime,
                    UpdateTime
                ) VALUES (
                    ?, 0, 0, '', '', ?, 1, ?, ?
                )
                ON DUPLICATE KEY UPDATE
                    RatingSum = RatingSum + ?,
                    RatingCount = RatingCount + 1,
                    UpdateTime = ?
            `, [roleId, rating, ts, ts, rating, ts]);
            const r = await this.astrologyUserModel.executeByQuery(rawQuery);
            logger.info({ ctx, roleId, rating, r }, "onRatingEventOK");
        }
        catch (err) {
            logger.error({ ctx, err, roleId, rating }, "onRatingEventError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getUserInfoMap(ctx, roleIds) {
        try {
            const profiles = await this.astrologyUserModel.findMany({
                where: { RoleId: roleIds },
                select: ['RoleId', 'Gender', 'AstroLevel', 'AstroType'],
                pagination: {
                    page: 1,
                    pageSize: roleIds.length
                }
            });
            const resp = {};
            for (const profile of profiles) {
                resp[profile.RoleId] = {
                    roleId: profile.RoleId,
                    gender: profile.Gender,
                    astroLevel: profile.AstroLevel || 0,
                    astroType: profile.AstroType || 0,
                };
            }
            return resp;
        }
        catch (err) {
            logger.error({ ctx, err, roleIds }, "getUserInfoMapError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getShowRoleInfoMap(ctx, roleIds) {
        try {
            const roleInfos = await this.astrologyRoleInfoModel.findMany({
                where: { RoleId: roleIds },
                select: ['RoleId', 'RoleName', 'Gender', 'SubGender', 'JobId', 'HeadPaintId', 'BodyPaintId'],
                pagination: {
                    page: 1,
                    pageSize: roleIds.length
                }
            });
            const resp = {};
            for (const roleInfo of roleInfos) {
                resp[roleInfo.RoleId] = {
                    roleId: roleInfo.RoleId,
                    roleName: roleInfo.RoleName || "",
                    jobId: roleInfo.JobId,
                    gender: roleInfo.Gender,
                    subGender: roleInfo.SubGender,
                    headPaintId: roleInfo.HeadPaintId,
                    bodyPaintId: roleInfo.BodyPaintId,
                };
            }
            return resp;
        }
        catch (err) {
            logger.error({ ctx, err, roleIds }, "getShowRoleInfoMapError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    getAverageRating(ratingSum, ratingCount, isMatchMinRoleIdCount) {
        if (isMatchMinRoleIdCount) {
            return Number((ratingSum / ratingCount).toFixed(config_all_1.astrologyCfg.avgRatingDecimalPlaces));
        }
        return 0;
    }
    async getUserShowProfileForServer(ctx, roleId) {
        const profile = await this.astrologyUserModel.findOne({ RoleId: roleId });
        if (!profile) {
            throw errorCode_1.errorCodesV2.AstrologyUserNotFound;
        }
        const totalReviews = profile.RatingCount;
        // 是否满足计算平均值的最小角色数
        const isMatchMinRoleIdCount = await bazaarRatingService_1.BazaarRatingService.getInstance().getCommentBeRatingRoleGteMinNum(ctx, roleId, config_all_1.astrologyCfg.avgRatingMinRoleIdCount);
        const averageRating = this.getAverageRating(profile.RatingSum, totalReviews, isMatchMinRoleIdCount);
        const resp = {
            roleId: roleId,
            averageRating,
            totalReviews,
        };
        logger.debug({ ctx, roleId, resp }, "getUserShowProfileForServerOK");
        return resp;
    }
    async getUserShowProfile(ctx, roleId) {
        let roleInfo = await this.astrologyRoleInfoModel.findOne({ RoleId: roleId }, ['RoleId', 'RoleName', 'Gender', 'SubGender', 'JobId', 'HeadPaintId', 'BodyPaintId']);
        if (!roleInfo) {
            logger.warn({ ctx, roleId }, "roleInfoNotFound");
            roleInfo = { RoleId: roleId, RoleName: "", Gender: 0, SubGender: 0, JobId: 0, HeadPaintId: 0, BodyPaintId: 0 };
        }
        const profile = await this.astrologyUserModel.findOne({ RoleId: roleId });
        if (!profile) {
            throw errorCode_1.errorCodesV2.AstrologyUserNotFound;
        }
        const totalReviews = profile.RatingCount;
        // 是否满足计算平均值的最小角色数
        const isMatchMinRoleIdCount = await bazaarRatingService_1.BazaarRatingService.getInstance().getCommentBeRatingRoleGteMinNum(ctx, roleId, config_all_1.astrologyCfg.avgRatingMinRoleIdCount);
        const averageRating = this.getAverageRating(profile.RatingSum, totalReviews, isMatchMinRoleIdCount);
        const resp = {
            roleId: roleId,
            roleInfo: {
                roleId: roleId,
                roleName: roleInfo.RoleName,
                jobId: roleInfo.JobId,
                gender: roleInfo.Gender,
                subGender: roleInfo.SubGender,
                headPaintId: roleInfo.HeadPaintId,
                bodyPaintId: roleInfo.BodyPaintId,
            },
            userInfo: {
                gender: profile.Gender,
                birthTime: this.convertToBirthTimeStr(profile.BirthTime),
                birthPlace: this.fromPlaceStrToLocation(profile.BirthPlace),
                currentPlace: this.fromPlaceStrToLocation(profile.CurrentPlace),
                astroLevel: profile.AstroLevel || 0,
                astroType: profile.AstroType || 0,
            },
            averageRating,
            totalReviews,
        };
        logger.debug({ ctx, roleId, resp }, "getUserShowProfileOK");
        return resp;
    }
    convertToUserHoroscopeInfo(profile) {
        const birthTime = moment(profile.BirthTime);
        const birthPlace = this.fromPlaceStrToLocation(profile.BirthPlace);
        const currentPlace = this.fromPlaceStrToLocation(profile.CurrentPlace);
        const userInfo = {
            user_gender: profile.Gender === 0 /* EAstrologyUserGender.MALE */ ? "男" : "女",
            birthdate_year: birthTime.year(),
            // moment.js的month()方法返回0-11表示1-12月,所以需要+1转换为1-12月
            birthdate_month: birthTime.month() + 1,
            birthdate_day: birthTime.date(),
            birthdate_hour: birthTime.hour(),
            birthdate_minute: birthTime.minute(),
            birth_province: birthPlace.province,
            birth_city: birthPlace.city,
            birth_district: birthPlace.district,
            now_province: currentPlace.province,
            now_city: currentPlace.city,
            now_district: currentPlace.district,
        };
        return userInfo;
    }
    async updateUserDailyForecast(ctx, roleId, ds, fortuneScore) {
        try {
            const now = Date.now();
            const r = await this.astrologyUserDailyForecastModel.createOrUpdate({
                RoleId: roleId,
                DS: ds,
                FortuneScore: fortuneScore,
                CreateTime: now,
                UpdateTime: now,
            }, {
                RoleId: roleId,
                DS: ds,
                FortuneScore: fortuneScore,
                UpdateTime: now,
            });
            logger.info({ ctx, roleId, ds, fortuneScore, r }, "updateUserDailyForecastOK");
        }
        catch (err) {
            logger.error({ ctx, err, roleId, ds, fortuneScore }, "updateUserDailyForecastError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getForecastFortuneDailyAvgScore(ctx, ds) {
        try {
            const query = this.astrologyUserDailyForecastModel.scope().where({ DS: ds }).avg('FortuneScore as avgScore');
            const r = await this.astrologyUserDailyForecastModel.executeByQuery(query);
            if (r.length === 0) {
                return 0;
            }
            return r[0].avgScore || 0;
        }
        catch (err) {
            logger.error({ ctx, err, ds }, "getForecastFortuneDailyAvgScoreError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async verifyUserNotRegister(ctx, roleId) {
        const profile = await this.astrologyUserModel.findOne({ RoleId: roleId }, ['RoleId']);
        if (profile && profile.RoleId) {
            logger.warn({ ctx, roleId }, "curRoleIdAlreadyRegistered");
            throw errorCode_1.errorCodesV2.AstrologyUserAlreadyRegistered;
        }
    }
}
exports.AstrologyUserService = AstrologyUserService;
//# sourceMappingURL=astrologyUserService.js.map
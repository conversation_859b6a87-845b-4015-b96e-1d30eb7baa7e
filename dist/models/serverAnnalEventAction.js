"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerAnnalEventActionModel = exports.ServerAnnalEventActionCols = void 0;
const baseModel_1 = require("./baseModel");
exports.ServerAnnalEventActionCols = ['ID', 'RoleId', 'EventId', 'Action', 'Status', 'CreateTime'];
class ServerAnnalEventActionModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("nsh_server_annal_event_action");
    }
}
exports.ServerAnnalEventActionModel = new ServerAnnalEventActionModelClass();
//# sourceMappingURL=serverAnnalEventAction.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenIdAdminModel = exports.OpenIdAdminModelClass = exports.openIdPriv = void 0;
const BaseModel2_1 = require("./BaseModel2");
const constants_1 = require("../common/constants");
var openIdPriv;
(function (openIdPriv) {
    openIdPriv[openIdPriv["admin"] = 0] = "admin";
    openIdPriv[openIdPriv["clubAdmin"] = 1] = "clubAdmin";
    openIdPriv[openIdPriv["competitionAdmin"] = 2] = "competitionAdmin";
    openIdPriv[openIdPriv["pickMomentAdmin"] = 3] = "pickMomentAdmin";
    openIdPriv[openIdPriv["npc"] = 4] = "npc";
})(openIdPriv || (exports.openIdPriv = openIdPriv = {}));
class OpenIdAdminModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.openIdAdmin);
    }
    async getPrivListFromOPenId(openId) {
        let record = await this.findOne({ OpenId: openId });
        if (!record) {
            return [];
        }
        let privList = record.Priv.split(',').map(item => parseInt(item, 10));
        return privList;
    }
    async checkSuperAdmin(openId) {
        let privList = await exports.OpenIdAdminModel.getPrivListFromOPenId(openId);
        return privList.indexOf(openIdPriv.admin) !== -1;
    }
    async checkClubAdmin(openId) {
        let privList = await exports.OpenIdAdminModel.getPrivListFromOPenId(openId);
        return privList.indexOf(openIdPriv.admin) !== -1 || privList.indexOf(openIdPriv.clubAdmin) !== -1;
    }
    async checkCompetitionAdmin(openId) {
        let privList = await exports.OpenIdAdminModel.getPrivListFromOPenId(openId);
        return privList.indexOf(openIdPriv.admin) !== -1 || privList.indexOf(openIdPriv.competitionAdmin) !== -1;
    }
    async checkPickMomentAdmin(openId) {
        let privList = await exports.OpenIdAdminModel.getPrivListFromOPenId(openId);
        return privList.indexOf(openIdPriv.admin) !== -1 || privList.indexOf(openIdPriv.pickMomentAdmin) !== -1;
    }
    async checkNpcAdmin(openId) {
        let privList = await exports.OpenIdAdminModel.getPrivListFromOPenId(openId);
        return privList.indexOf(openIdPriv.admin) !== -1 || privList.indexOf(openIdPriv.npc) !== -1;
    }
}
exports.OpenIdAdminModelClass = OpenIdAdminModelClass;
exports.OpenIdAdminModel = new OpenIdAdminModelClass();
//# sourceMappingURL=openIdAdmin.js.map
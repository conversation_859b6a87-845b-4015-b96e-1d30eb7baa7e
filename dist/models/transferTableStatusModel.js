"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransferTableStatusModel = exports.TransferTableStatus = void 0;
const util_1 = require("../common/util");
const logger_1 = require("../logger");
const BaseModel2_1 = require("./BaseModel2");
const logger = (0, logger_1.clazzLogger)("models/transferTableStatusModel");
var TransferTableStatus;
(function (TransferTableStatus) {
    TransferTableStatus[TransferTableStatus["INIT"] = 0] = "INIT";
    TransferTableStatus[TransferTableStatus["MigrateOk"] = 1] = "MigrateOk";
    TransferTableStatus[TransferTableStatus["MigrateFail"] = 2] = "MigrateFail";
})(TransferTableStatus || (exports.TransferTableStatus = TransferTableStatus = {}));
class TransferTableStatusModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_transfer_table_status");
    }
    async markInitTransferTableStatus(oldId, newId, tableName, col) {
        try {
            const hashKey = (0, util_1.hexMd5)(`${oldId}-${newId}-${tableName}-${col}`);
            const record = {
                OldId: oldId,
                NewId: newId,
                TableName: tableName,
                HashKey: hashKey,
                Col: col,
                Cost: 0,
                Status: TransferTableStatus.INIT,
                Log: "",
                CreateTime: Date.now(),
                UpdateTime: Date.now(),
            };
            const markId = await this.insert(record);
            logger.info({ oldId, newId, tableName }, "MarkInitTransferTableStatusOk");
            return { ...record, ID: markId };
        }
        catch (err) {
            logger.error({ oldId, newId, tableName }, "MarkInitTransferTableStatusError");
            return null;
        }
    }
    // check table transfer exists
    async existsTransferTableStatus(oldId, newId, tableName, col) {
        try {
            const record = await this.findOne({ OldId: oldId, NewId: newId, TableName: tableName, Col: col }, ["ID"]);
            if (record && record.ID) {
                return true;
            }
            else {
                return false;
            }
        }
        catch (err) {
            logger.error({ oldId, newId, tableName }, "ExistsTransferTableStatusError");
            return false;
        }
    }
    // mark transfer table migrate ok
    async markTransferTableStatusOk(r, log, costTime) {
        try {
            const logStr = JSON.stringify(log);
            const upRet = await this.updateByCondition({ ID: r.ID }, { Status: TransferTableStatus.MigrateOk, Log: logStr, Cost: costTime, UpdateTime: Date.now() });
            logger.info({ r, upRet }, "MarkTransferTableStatusSuccessOk");
            return upRet.affectedRows > 0;
        }
        catch (err) {
            logger.error({ r }, "MarkTransferTableStatusSuccessError");
            return false;
        }
    }
    // mark transfer table migrate fail
    async markTransferTableStatusFail(r, log, costTime) {
        try {
            const logStr = JSON.stringify(log);
            const upRet = await this.updateByCondition({ ID: r.ID }, { Status: TransferTableStatus.MigrateFail, Log: logStr, Cost: costTime, UpdateTime: Date.now() });
            logger.info({ r, upRet }, "MarkTransferTableStatusFailOk");
            return upRet.affectedRows > 0;
        }
        catch (err) {
            logger.error({ r }, "MarkTransferTableStatusFailError");
            return false;
        }
    }
}
exports.TransferTableStatusModel = new TransferTableStatusModelClass();
//# sourceMappingURL=transferTableStatusModel.js.map
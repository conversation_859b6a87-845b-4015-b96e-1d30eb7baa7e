"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerAnnalEventModel = exports.ServerAnnalEventCols = void 0;
const baseModel_1 = require("./baseModel");
exports.ServerAnnalEventCols = ['ID', 'UUID', 'PlayerIds', 'PlayerNames', 'GuildIds', 'GuildNames', 'ServerId', 'EventType', 'Category', 'EventArgs', 'EventTime', 'CreateTime', 'UpVote', 'DownVote', 'Status', 'First'];
class ServerAnnalEventModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("nsh_server_annal_event");
    }
}
exports.ServerAnnalEventModel = new ServerAnnalEventModelClass();
//# sourceMappingURL=serverAnnalEvent.js.map
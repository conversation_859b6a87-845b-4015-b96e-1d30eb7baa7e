"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonthCardChargeModel = exports.MonthCardChargeCols = void 0;
const baseModel_1 = require("./baseModel");
exports.MonthCardChargeCols = ['id', 'urs', 'serverId', 'channel', 'orderId', 'buyTime', 'duration', 'createTime'];
class MonthCardChargeModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("nsh_cloud_game_month_card_charge");
    }
}
exports.MonthCardChargeModel = new MonthCardChargeModelClass();
//# sourceMappingURL=monthCardCharge.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const moment = require("moment");
const weekRankReward_1 = require("../services/weekRankReward");
function main() {
    let date = moment().day(-6).toDate(); // 周日05分取上周六那周的排行
    return (0, weekRankReward_1.dispatchWeekRenqiReward)(date);
}
main().then(() => {
    console.log('Done');
    process.exit(0);
}).catch(err => {
    console.error(err);
    process.exit(-1);
});
//# sourceMappingURL=rewardLastWeekRenQi.js.map
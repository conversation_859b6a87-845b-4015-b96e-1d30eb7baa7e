"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronPattern = exports.TaskManager = void 0;
const cron_1 = require("cron");
const logger_1 = require("../common/logger");
const request_1 = require("../common/request");
const logger = (0, logger_1.getLogger)('cronTasks');
async function getTryCatchWithoutThrow(url, fail = false) {
    if (fail) {
        url += '/fail';
    }
    try {
        await (0, request_1.get)(url, null);
    }
    catch (err) { }
}
class Task {
    constructor(option) {
        this.running = false;
        this.name = option.name;
        this.cronTime = option.cronTime;
        this.execute = option.execute;
        this.logger = option.logger;
        this.manager = option.manager;
        this.checkUrl = option.checkUrl;
    }
    async run() {
        if (!this.running) {
            try {
                this.running = true;
                let startTime = Date.now();
                await this.execute();
                let endTime = Date.now();
                this.manager.recordFinishInfo(this, startTime, endTime);
                if (this.checkUrl) {
                    getTryCatchWithoutThrow(this.checkUrl);
                }
            }
            catch (err) {
                this.logger.error(err);
                if (this.checkUrl) {
                    getTryCatchWithoutThrow(this.checkUrl, true);
                }
            }
            finally {
                this.running = false;
            }
        }
        else {
            this.logger.warn('Fire running task, slow down trigger frequency!');
        }
    }
    static create(option) {
        return new Task(option);
    }
}
const LOG_RUN_INFO_INTERVAL = 15 * 60 * 1000;
class TaskManager {
    constructor(name, flags) {
        this.jobMap = new Map();
        this.finishInfo = new Map();
        this.name = name;
        this.logger = logger.child({ manager: this.name });
        this.flags = flags;
    }
    static create(name, flags) {
        return new TaskManager(name, flags);
    }
    addByTask(task, option) {
        let job = new cron_1.CronJob({
            cronTime: task.cronTime,
            onTick: async function () {
                try {
                    await task.run();
                }
                catch (err) {
                    logger.error(err);
                }
            },
            runOnInit: option.runOnInit || false
        });
        this.jobMap.set(task.name, job);
    }
    add(option) {
        let logger = this.logger.child({ task: option.name });
        let params = Object.assign({}, option, { logger: logger, manager: this });
        let task = Task.create(params);
        this.addByTask(task, option);
    }
    isEnableTask(taskName) {
        return !!this.flags[taskName];
    }
    recordFinishInfo(task, startTime, endTime) {
        this.finishInfo.set(task.name, { start: startTime, end: endTime });
    }
    printFinishInfo() {
        for (let [name, td] of this.finishInfo) {
            let duration = td.end - td.start;
            this.logger.info({ task: name, start: td.start, end: td.end, duration: duration }, 'LastFinishTaskTime');
        }
    }
    runAll() {
        for (let [name, job] of this.jobMap) {
            if (this.isEnableTask(name)) {
                this.logger.info('Begin to Run task ' + name);
                job.start();
            }
            else {
                this.logger.warn('Task:' + name + ' is disabled');
            }
        }
        setInterval(() => {
            this.printFinishInfo();
        }, LOG_RUN_INFO_INTERVAL);
    }
}
exports.TaskManager = TaskManager;
exports.CronPattern = {
    EVERY_MINUTE: '* * * * *',
    EVERY_TEN_MINUTE: '*/10 * * * *',
    EVERY_THIRTY_MINUTE: '*/30 * * * *',
    EVERY_TWO_HOUR: '0 */2 * * *',
    EVERY_SECOND: '* * * * * *'
};
//# sourceMappingURL=taskManager.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const path = require("path");
const os = require("os");
const childProcess = require("child_process");
const taskManager_1 = require("./taskManager");
const config = require("../common/config");
const config_1 = require("../common/config");
const paopaoAlarm_1 = require("../common/paopaoAlarm");
let TaskSwitch = {
    fatalAlarm: true,
};
let watchFileSet = new Set();
let manager = taskManager_1.TaskManager.create('transCrons', TaskSwitch);
manager.add({
    name: "fatalAlarm",
    cronTime: taskManager_1.CronPattern.EVERY_MINUTE,
    execute: async function () {
        return fatalAlarmCrons();
    }
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
async function fatalAlarmCrons() {
    const LogDir = config.log.dir || path.join(__dirname, "../../log");
    let files = fs.readdirSync(LogDir);
    let fatalReg = /[\s\S]+fatal.log/;
    for (let fileName of files) {
        if (fatalReg.test(fileName) && !watchFileSet.has(fileName)) {
            watchFileSet.add(fileName);
            matchAFatalFile(LogDir, fileName);
        }
    }
}
async function matchAFatalFile(LogDir, fileName) {
    let filePath = path.join(LogDir, fileName);
    fs.watchFile(filePath, {
        interval: config_1.fatalAlarmCfg.interval
    }, (curr, prev) => {
        let theLastLine = childProcess.execSync(`tail -n1 ${filePath}`);
        for (let reg of config_1.fatalAlarmCfg.ignoreRegArr) {
            if (reg.test(theLastLine)) {
                return;
            }
        }
        (0, paopaoAlarm_1.paoPaoAlarm3)(`环境: ${config.log.env},出现致命错误,请及时查看
机器: ${os.hostname}
日志路径: ${filePath}
错误内容: ${theLastLine.toString()}`);
    });
}
//# sourceMappingURL=fatalAlarm.js.map
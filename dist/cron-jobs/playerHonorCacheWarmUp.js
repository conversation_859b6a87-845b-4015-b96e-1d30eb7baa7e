"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.playerHonorCacheWarmUp = void 0;
const ModelManager = require("../models/ModelManager");
const db_1 = require("../common/db");
const bluebird = require("bluebird");
const logger_1 = require("../logger");
const config_1 = require("../common/config");
const logger = logger_1.cronLogger.child({ clazz: "cron-jobs/playerHonorCacheWarmUp" });
function getNextRows(lastId) {
    return __awaiter(this, void 0, void 0, function* () {
        const HonorBindModel = ModelManager.getModelByTableName('nsh_honor_bind', db_1.DBNode.Slave);
        const query = HonorBindModel.scope()
            .where('ID', '>', lastId)
            .orderBy('ID', 'asc')
            .limit(config_1.playerHonorCfg.pageSize);
        const rows = yield HonorBindModel.smartQuery(query, ['ID', 'RoleId']);
        return rows;
    });
}
function warmUpCache(roleIds) {
    return __awaiter(this, void 0, void 0, function* () {
        for (let roleId of roleIds) {
            yield bluebird.delay(config_1.playerHonorCfg.delayInterval);
        }
    });
}
function playerHonorCacheWarmUp() {
    return __awaiter(this, void 0, void 0, function* () {
        let lastId = 0;
        logger.info({ lastId }, "PlayersHonorCacheWarmUpStart");
        let rows = yield getNextRows(lastId);
        while (rows && rows.length > 0) {
            yield warmUpCache(rows.map(r => r.RoleId));
            lastId = rows[rows.length - 1].ID;
            logger.info({ lastId, rowsCnt: rows.length }, "PlayersHonorCacheWarmUpProcess");
            rows = yield getNextRows(lastId);
        }
        logger.info({ lastId }, "PlayersHonorCacheWarmUpEnd");
    });
}
exports.playerHonorCacheWarmUp = playerHonorCacheWarmUp;
//# sourceMappingURL=playerHonorCacheWarmUp.js.map
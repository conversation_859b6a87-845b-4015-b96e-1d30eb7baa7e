"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processReadyTask = processReadyTask;
exports.triggerDelayTask = triggerDelayTask;
const delayTaskCore_1 = require("./delayTaskCore");
const logger_1 = require("../logger");
const HotMomentsCache = require("../services/HotMomentsCache");
const FcmService = require("../services/fcm");
const playerTransfer_1 = require("../services/playerTransfer");
const guildPolemic_1 = require("../services/guildPolemic");
const marriageTransfer_1 = require("../services/marriageTransfer");
class DelayTaskHandlerManger {
    static async defaultHandler(job) {
        logger_1.logger.error({ job: job }, "DelayTaskNotRegisterHandler");
        return false;
    }
    static add(topic, handler) {
        this.map.set(topic, handler);
    }
    static hasHandler(topic) {
        return this.map.has(topic);
    }
    static getHandler(topic) {
        return this.map.get(topic) || this.defaultHandler;
    }
}
DelayTaskHandlerManger.map = new Map();
async function processReadyTask() {
    let job = await delayTaskCore_1.PyqDelayQueue.popReady();
    if (job && job.id) {
        let handler = DelayTaskHandlerManger.getHandler(job.topic);
        try {
            logger_1.logger.info({ job: job }, "ProcessReadyTaskJob");
            let handleRet = await handler(job.body);
            return handleRet;
        }
        catch (err) {
            logger_1.logger.error({ err: err }, "HandlerTaskError");
            return false;
        }
    }
}
function triggerDelayTask() {
    return delayTaskCore_1.PyqDelayQueue.onTick();
}
// here to register delay task event handler
function registerAllTaskHandler() {
    DelayTaskHandlerManger.add(delayTaskCore_1.DELAY_TOPICS.DownHotMomentTopic, function (payload) {
        return HotMomentsCache.downHotMoments([payload.momentId], payload.serverId);
    });
    DelayTaskHandlerManger.add(delayTaskCore_1.DELAY_TOPICS.QueryPiResultByAi, function (payload) {
        logger_1.logger.info({ payload }, "JobHandleByFcmService");
        return FcmService.handleQueryPiByAi(payload);
    });
    DelayTaskHandlerManger.add(delayTaskCore_1.DELAY_TOPICS.PlayerTransfer, function (payload) {
        return (0, playerTransfer_1.playerTransferTaskHandler)(payload);
    });
    DelayTaskHandlerManger.add(delayTaskCore_1.DELAY_TOPICS.UpdatePolemicHot, function (payload) {
        return (0, guildPolemic_1.updatePolemicHotTaskHandler)(payload);
    });
    DelayTaskHandlerManger.add(delayTaskCore_1.DELAY_TOPICS.MarriageTransfer, function (payload) {
        return (0, marriageTransfer_1.marriageTransferTaskHandler)(payload);
    });
}
registerAllTaskHandler();
//# sourceMappingURL=delayTasks.js.map
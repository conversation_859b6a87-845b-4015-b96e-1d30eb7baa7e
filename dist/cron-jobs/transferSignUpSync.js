"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const taskManager_1 = require("./taskManager");
const transferSignUp_1 = require("../services/transferSignUp");
let manager = taskManager_1.TaskManager.create('transferSignUpSync', { transferSignUpSync: true });
manager.add({
    name: 'transferSignUpSync',
    cronTime: taskManager_1.CronPattern.EVERY_SECOND,
    execute: function () {
        return (0, transferSignUp_1.dealTransferSignUpQueue)();
    }
});
manager.runAll();
//# sourceMappingURL=transferSignUpSync.js.map
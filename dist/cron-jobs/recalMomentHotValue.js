"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePyqMomentsHot = updatePyqMomentsHot;
exports.updateMomentHot = updateMomentHot;
const _ = require("lodash");
const ModelManager = require("../models/ModelManager");
const moment_1 = require("../services/moment");
const bluebird = require("bluebird");
const PyqMoments_1 = require("../models/PyqMoments");
const db_1 = require("../common/db");
const SCAN_MOMENT_MAX_SIZE = 100000; // 限制扫表的最大数量
const SCAN_MOMENT_PAGE_SIZE = 500; // 每次取多少来更新
const UPDATE_MOMENT_PER_DELAY = 50; // 更新时每条更新的间隔时间，减少对DB的冲击
async function findScanIds(page) {
    const MomentModel = ModelManager.getModelByTableName('nsh_moment', db_1.DBNode.Slave);
    const query = MomentModel.normalScope()
        .select('ID')
        .orderBy('Hot', 'desc');
    const rows = await MomentModel.queryWithPagination(query, { page: page, pageSize: SCAN_MOMENT_PAGE_SIZE });
    return _.map(rows, 'ID');
}
async function updateHotByMomentIds(ids, now) {
    for (let i = 0; i < ids.length; i++) {
        const id = ids[i];
        const moment = await PyqMoments_1.Moment.findById(id, ['CreateTime', 'HotState', 'Hot']);
        const newHot = (0, moment_1.calHot)(moment, 'time', null, {
            now: now
        });
        const updateValues = { HotState: JSON.stringify(newHot.state), Hot: newHot.hot };
        const query = PyqMoments_1.Moment.scope().where('ID', id).update(updateValues);
        await PyqMoments_1.Moment.executeByQuery(query);
        await bluebird.delay(UPDATE_MOMENT_PER_DELAY);
    }
}
async function updatePyqMomentsHot() {
    const now = Date.now(); // 同一批计算使用同一个当前时间来计算热度
    const totalPage = Math.ceil(SCAN_MOMENT_MAX_SIZE / SCAN_MOMENT_PAGE_SIZE);
    for (let i = 1; i <= totalPage; i++) {
        const mIds = await findScanIds(i);
        if (_.isEmpty(mIds)) {
            break;
        }
        else {
            await updateHotByMomentIds(mIds, now);
        }
    }
}
async function updateMomentHot() {
    try {
        let r2 = await updatePyqMomentsHot();
    }
    catch (err) {
        console.error(err);
    }
}
//# sourceMappingURL=recalMomentHotValue.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DELAY_TOPICS = exports.PyqDelayQueue = void 0;
exports.addDelayTask = addDelayTask;
const delayQueue_1 = require("../common/delayQueue");
const config_1 = require("../common/config");
exports.PyqDelayQueue = new delayQueue_1.DelayQueue(config_1.delayTaskCfg.name);
exports.DELAY_TOPICS = {
    DownHotMomentTopic: "DownHotMomentTopic",
    PlayerTransfer: "PlayerTransfer",
    MarriageTransfer: "MarriageTransfer",
    UpdatePolemicHot: "UpdatePolemicHot",
    QueryPiResultByAi: "QueryPiResultByAi",
};
function addDelayTask(task) {
    let addRet = exports.PyqDelayQueue.addJob(task);
    return addRet;
}
//# sourceMappingURL=delayTaskCore.js.map
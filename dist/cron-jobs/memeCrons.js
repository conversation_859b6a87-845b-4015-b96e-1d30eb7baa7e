"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config = require("../common/config");
const dataMigrate_1 = require("../components/meme/dataMigrate");
const taskManager_1 = require("./taskManager");
const TaskSwitch = config.CronTaskSwitch;
const manager = taskManager_1.TaskManager.create("memeCrons", TaskSwitch);
// 热度值随时间衰减
manager.add({
    name: "MemeDataMigrate",
    cronTime: "0 */8 * * *",
    execute: async function () {
        return (0, dataMigrate_1.memeMigrateFromNosToFp)();
    },
    runOnInit: true,
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=memeCrons.js.map